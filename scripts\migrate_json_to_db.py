#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON数据迁移到数据库脚本
将 pricing_projects.json 和 feature_sections.json 数据迁移到 te_manage.db
"""

import os
import sys
import json
import shutil
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(__file__)
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from modules.quick_pricing.database import ProjectDAO, FeatureDAO

def load_json_data(file_path):
    """加载JSON数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载数据失败 {file_path}: {e}")
        return {}

def backup_json_files():
    """备份原始JSON文件"""
    print("💾 备份原始JSON文件...")
    
    files_to_backup = [
        'data/pricing_projects.json',
        'data/feature_sections.json'
    ]
    
    backup_dir = 'data/backup'
    os.makedirs(backup_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            backup_path = os.path.join(backup_dir, f"{timestamp}_{filename}")
            
            try:
                shutil.copy2(file_path, backup_path)
                print(f"✅ 备份文件: {file_path} -> {backup_path}")
            except Exception as e:
                print(f"❌ 备份文件失败 {file_path}: {e}")

def migrate_projects():
    """迁移工程数据"""
    print("\n🔄 开始迁移工程数据...")
    
    # 加载JSON数据
    projects_file = os.path.join('data', 'pricing_projects.json')
    data = load_json_data(projects_file)
    projects = data.get('projects', [])
    
    if not projects:
        print("⚠️ 没有找到工程数据")
        return 0
    
    project_dao = ProjectDAO()
    migrated_count = 0
    
    for project in projects:
        try:
            # 检查工程是否已存在（根据序号）
            existing = project_dao.get_project_by_xuhao(project.get('序号'))
            if existing:
                print(f"⏭️ 工程 {project.get('工程名称')} (序号: {project.get('序号')}) 已存在，跳过")
                continue
            
            # 准备工程数据，添加project_id字段
            project_data = project.copy()
            project_data['project_id'] = project.get('序号')  # 使用序号作为project_id
            
            # 移除不需要的字段
            project_data.pop('指标汇总状态', None)
            project_data.pop('指标汇总时间', None)
            project_data.pop('备注', None)
            
            # 创建工程
            result_id = project_dao.create_project(project_data)
            print(f"✅ 迁移工程: {project.get('工程名称')} (序号: {project.get('序号')}) -> ID: {result_id}")
            migrated_count += 1
            
        except Exception as e:
            print(f"❌ 迁移工程失败 {project.get('工程名称')}: {e}")
    
    print(f"📊 工程迁移完成，共迁移 {migrated_count} 个工程")
    return migrated_count

def migrate_features():
    """迁移特征段数据"""
    print("\n🔄 开始迁移特征段数据...")
    
    # 加载JSON数据
    features_file = os.path.join('data', 'feature_sections.json')
    data = load_json_data(features_file)
    features = data.get('feature_sections', [])
    
    if not features:
        print("⚠️ 没有找到特征段数据")
        return 0
    
    project_dao = ProjectDAO()
    feature_dao = FeatureDAO()
    migrated_count = 0
    
    for feature in features:
        try:
            # 检查特征段是否已存在（根据序号）
            existing = feature_dao.get_feature_by_id(feature.get('序号'))
            if existing:
                print(f"⏭️ 特征段 {feature.get('特征段名称')} (序号: {feature.get('序号')}) 已存在，跳过")
                continue
            
            # 获取对应的工程
            工程序号 = feature.get('工程序号')
            project = project_dao.get_project_by_xuhao(工程序号)
            if not project:
                print(f"⚠️ 特征段 {feature.get('特征段名称')} 对应的工程 (序号: {工程序号}) 不存在，跳过")
                continue
            
            # 准备特征段数据
            feature_data = feature.copy()
            feature_data['project_id'] = project['project_id']
            
            # 处理边界条件数据
            if '边界条件' in feature_data:
                边界条件 = feature_data.pop('边界条件')
                feature_data.update({
                    '风速': 边界条件.get('风速'),
                    '覆冰': 边界条件.get('覆冰'),
                    '回路数': 边界条件.get('回路数'),
                    '导线规格': 边界条件.get('导线规格')
                })
            
            # 处理组价状态字段
            组价计算状态 = feature_data.pop('组价计算状态', '未组价')
            feature_data['组价状态'] = 组价计算状态

            # 移除不需要的字段
            feature_data.pop('工程序号', None)
            
            # 添加默认地形数据（如果没有的话）
            terrain_fields = ['平地', '丘陵', '山地', '高山', '峻岭', '泥沼', '河网']
            for field in terrain_fields:
                if field not in feature_data:
                    feature_data[field] = 0
            
            # 创建特征段
            result_id = feature_dao.create_feature(feature_data)
            print(f"✅ 迁移特征段: {feature.get('特征段名称')} (序号: {feature.get('序号')}) -> ID: {result_id}")
            migrated_count += 1
            
        except Exception as e:
            print(f"❌ 迁移特征段失败 {feature.get('特征段名称')}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"📊 特征段迁移完成，共迁移 {migrated_count} 个特征段")
    return migrated_count

def update_project_feature_counts():
    """更新工程的特征段数量"""
    print("\n🔄 更新工程特征段数量...")
    
    project_dao = ProjectDAO()
    feature_dao = FeatureDAO()
    
    projects = project_dao.get_all_projects()
    
    for project in projects:
        try:
            features = feature_dao.get_features_by_project(project['project_id'])
            feature_count = len(features)
            
            project_dao.update_project(project['project_id'], {'特征段数量': feature_count})
            print(f"✅ 更新工程 {project['工程名称']} 特征段数量: {feature_count}")
            
        except Exception as e:
            print(f"❌ 更新工程特征段数量失败 {project['工程名称']}: {e}")

def verify_migration():
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    project_dao = ProjectDAO()
    feature_dao = FeatureDAO()
    
    # 统计数据库中的数据
    projects = project_dao.get_all_projects()
    total_features = 0
    
    print(f"📊 数据库中共有 {len(projects)} 个工程:")
    
    for project in projects:
        features = feature_dao.get_features_by_project(project['project_id'])
        feature_count = len(features)
        total_features += feature_count
        
        print(f"   - {project['工程名称']} (序号: {project['序号']}): {feature_count} 个特征段")
    
    print(f"📊 数据库中共有 {total_features} 个特征段")
    
    # 对比JSON文件中的数据
    projects_file = os.path.join('data', 'pricing_projects.json')
    features_file = os.path.join('data', 'feature_sections.json')
    
    json_projects = load_json_data(projects_file).get('projects', [])
    json_features = load_json_data(features_file).get('feature_sections', [])
    
    print(f"📊 JSON文件中有 {len(json_projects)} 个工程，{len(json_features)} 个特征段")
    
    if len(projects) == len(json_projects) and total_features == len(json_features):
        print("✅ 迁移验证成功！数据完整性检查通过")
        return True
    else:
        print("⚠️ 迁移验证警告：数据数量不匹配，请检查迁移过程")
        return False

def main():
    """主函数"""
    print("🚀 开始JSON数据迁移到数据库...")
    print("=" * 60)
    
    # 检查数据库是否存在
    db_path = os.path.join('data', 'te_manage.db')
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在，请先运行 init_te_manage_db.py 创建数据库")
        return
    
    try:
        # 1. 备份原始文件
        backup_json_files()
        
        # 2. 迁移工程数据
        project_count = migrate_projects()
        
        # 3. 迁移特征段数据
        feature_count = migrate_features()
        
        # 4. 更新工程特征段数量
        update_project_feature_counts()
        
        # 5. 验证迁移结果
        success = verify_migration()
        
        if success:
            print("\n🎉 数据迁移完成！")
            print(f"📈 成功迁移 {project_count} 个工程，{feature_count} 个特征段")
            print("💡 提示：原始JSON文件已备份到 data/backup/ 目录")
        else:
            print("\n⚠️ 数据迁移完成，但验证发现问题，请检查数据")
        
    except Exception as e:
        print(f"\n💥 数据迁移失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
