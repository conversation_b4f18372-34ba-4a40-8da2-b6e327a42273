#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试气象区API功能的脚本
"""

import requests
import json

def test_feature_section_api():
    """测试特征段API是否正确返回气象区数据"""
    
    base_url = "http://localhost:5000"
    project_id = 1
    
    print("=== 测试特征段API气象区功能 ===")
    
    # 测试获取工程的所有特征段
    print(f"\n1. 测试获取工程{project_id}的所有特征段")
    try:
        response = requests.get(f"{base_url}/api/pricing/projects/{project_id}/feature_sections")
        if response.status_code == 200:
            sections = response.json()
            print(f"成功获取到{len(sections)}个特征段")
            
            for section in sections:
                section_id = section.get('序号')
                section_name = section.get('特征段名称')
                weather_zone = section.get('气象区')
                print(f"  - 特征段{section_id}: {section_name}, 气象区: {weather_zone}")
        else:
            print(f"请求失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"请求异常: {e}")
    
    # 测试获取单个特征段详情
    test_sections = [1, 19]  # 测试"分"和"合并"两种气象区
    
    for section_id in test_sections:
        print(f"\n2. 测试获取特征段{section_id}的详情")
        try:
            response = requests.get(f"{base_url}/api/pricing/projects/{project_id}/feature_sections/{section_id}")
            if response.status_code == 200:
                section = response.json()
                section_name = section.get('特征段名称')
                weather_zone = section.get('气象区')
                print(f"  特征段名称: {section_name}")
                print(f"  气象区: {weather_zone}")
                print(f"  气象区类型: {type(weather_zone)}")
                
                # 验证气象区值
                if weather_zone in ['分', '合并']:
                    print(f"  ✅ 气象区值有效")
                else:
                    print(f"  ❌ 气象区值无效: {weather_zone}")
            else:
                print(f"请求失败: {response.status_code}")
                print(response.text)
        except Exception as e:
            print(f"请求异常: {e}")

if __name__ == "__main__":
    test_feature_section_api()
