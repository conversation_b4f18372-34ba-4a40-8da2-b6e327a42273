#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为t_pricing_fetures表添加气象区字段的数据库迁移脚本
"""

import os
import sqlite3
import sys

def add_weather_zone_column():
    """为t_pricing_fetures表添加气象区字段"""
    
    # 获取数据库文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    db_path = os.path.join(project_root, 'data', 'te_manage.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查气象区字段是否已存在
        cursor.execute("PRAGMA table_info(t_pricing_fetures)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if '气象区' in columns:
            print("气象区字段已存在，无需添加")
            conn.close()
            return True
        
        # 添加气象区字段
        cursor.execute('''
            ALTER TABLE t_pricing_fetures 
            ADD COLUMN 气象区 TEXT DEFAULT '分'
        ''')
        
        # 为现有数据设置默认值
        cursor.execute('''
            UPDATE t_pricing_fetures 
            SET 气象区 = '分' 
            WHERE 气象区 IS NULL
        ''')
        
        conn.commit()
        print("成功为t_pricing_fetures表添加气象区字段")
        
        # 验证字段添加成功
        cursor.execute("SELECT COUNT(*) FROM t_pricing_fetures WHERE 气象区 = '分'")
        count = cursor.fetchone()[0]
        print(f"已为 {count} 条记录设置默认气象区值为'分'")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"添加气象区字段失败: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    success = add_weather_zone_column()
    sys.exit(0 if success else 1)
