#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试数据库功能
"""

import sqlite3
import os

def test_database():
    """直接测试数据库中的气象区数据"""
    
    db_path = os.path.join('data', 'te_manage.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        print("=== 检查表结构 ===")
        cursor.execute("PRAGMA table_info(t_pricing_fetures)")
        columns = cursor.fetchall()
        
        weather_zone_column = None
        for col in columns:
            if col[1] == '气象区':
                weather_zone_column = col
                break
        
        if weather_zone_column:
            print(f"✅ 气象区字段存在: {weather_zone_column}")
        else:
            print("❌ 气象区字段不存在")
            return
        
        # 查询测试数据
        print("\n=== 查询测试数据 ===")
        cursor.execute("SELECT 序号, 特征段名称, 气象区, project_id FROM t_pricing_fetures WHERE 序号 IN (1, 19)")
        rows = cursor.fetchall()
        
        for row in rows:
            print(f"特征段{row[0]}: {row[1]}, 气象区: '{row[2]}', 工程ID: {row[3]}")
        
        # 查询所有气象区值
        print("\n=== 所有气象区值统计 ===")
        cursor.execute("SELECT 气象区, COUNT(*) FROM t_pricing_fetures GROUP BY 气象区")
        stats = cursor.fetchall()
        
        for stat in stats:
            print(f"气象区 '{stat[0]}': {stat[1]} 个特征段")
        
        conn.close()
        print("\n✅ 数据库测试完成")
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

if __name__ == "__main__":
    test_database()
