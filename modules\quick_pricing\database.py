#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速组价模块数据库操作类
提供快速组价相关的数据库连接和CRUD操作
"""

import os
import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Any, Optional

class QuickPricingDB:
    """快速组价数据库操作基类"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            # 相对于模块目录的数据库路径
            current_dir = os.path.dirname(__file__)
            project_root = os.path.dirname(os.path.dirname(current_dir))
            db_path = os.path.join(project_root, 'data', 'te_manage.db')
        self.db_path = db_path
        
    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询并返回结果"""
        conn = self.get_connection()
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            results = [dict(row) for row in cursor.fetchall()]
            return results
        finally:
            conn.close()
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            return cursor.rowcount
        finally:
            conn.close()
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """执行插入操作并返回新记录的ID"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()

class ProjectDAO:
    """工程数据访问对象"""
    
    def __init__(self):
        self.db = QuickPricingDB()
    
    def get_all_projects(self) -> List[Dict]:
        """获取所有工程"""
        query = """
            SELECT * FROM t_pricing_projects 
            ORDER BY 序号
        """
        return self.db.execute_query(query)
    
    def get_project_by_id(self, project_id: int) -> Optional[Dict]:
        """根据project_id获取工程"""
        query = "SELECT * FROM t_pricing_projects WHERE project_id = ?"
        results = self.db.execute_query(query, (project_id,))
        return results[0] if results else None
    
    def get_project_by_xuhao(self, xuhao: int) -> Optional[Dict]:
        """根据序号获取工程"""
        query = "SELECT * FROM t_pricing_projects WHERE 序号 = ?"
        results = self.db.execute_query(query, (xuhao,))
        return results[0] if results else None
    
    def create_project(self, project_data: Dict) -> int:
        """创建新工程"""
        query = """
            INSERT INTO t_pricing_projects 
            (序号, project_id, 工程名称, 电压等级, 线路总长度, 特征段数量, 组价状态, 创建时间, 组价时间)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            project_data.get('序号'),
            project_data.get('project_id'),
            project_data.get('工程名称'),
            project_data.get('电压等级'),
            project_data.get('线路总长度'),
            project_data.get('特征段数量', 0),
            project_data.get('组价状态', '未组价'),
            project_data.get('创建时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            project_data.get('组价时间')
        )
        return self.db.execute_insert(query, params)
    
    def update_project(self, project_id: int, project_data: Dict) -> int:
        """更新工程信息"""
        query = """
            UPDATE t_pricing_projects 
            SET 工程名称=?, 电压等级=?, 线路总长度=?, 特征段数量=?, 组价状态=?, 组价时间=?
            WHERE project_id = ?
        """
        params = (
            project_data.get('工程名称'),
            project_data.get('电压等级'),
            project_data.get('线路总长度'),
            project_data.get('特征段数量'),
            project_data.get('组价状态'),
            project_data.get('组价时间'),
            project_id
        )
        return self.db.execute_update(query, params)
    
    def delete_project(self, project_id: int) -> int:
        """删除工程"""
        query = "DELETE FROM t_pricing_projects WHERE project_id = ?"
        return self.db.execute_update(query, (project_id,))
    
    def get_next_xuhao(self) -> int:
        """获取下一个序号"""
        query = "SELECT MAX(序号) as max_xuhao FROM t_pricing_projects"
        results = self.db.execute_query(query)
        max_xuhao = results[0]['max_xuhao'] if results and results[0]['max_xuhao'] else 0
        return max_xuhao + 1
    
    def get_next_project_id(self) -> int:
        """获取下一个project_id"""
        query = "SELECT MAX(project_id) as max_project_id FROM t_pricing_projects"
        results = self.db.execute_query(query)
        max_project_id = results[0]['max_project_id'] if results and results[0]['max_project_id'] else 0
        return max_project_id + 1

class FeatureDAO:
    """特征段数据访问对象"""
    
    def __init__(self):
        self.db = QuickPricingDB()
    
    def get_features_by_project(self, project_id: int) -> List[Dict]:
        """获取指定工程的所有特征段"""
        query = """
            SELECT * FROM t_pricing_fetures 
            WHERE project_id = ? 
            ORDER BY 序号
        """
        return self.db.execute_query(query, (project_id,))
    
    def get_feature_by_id(self, feature_id: int) -> Optional[Dict]:
        """根据序号获取特征段"""
        query = "SELECT * FROM t_pricing_fetures WHERE 序号 = ?"
        results = self.db.execute_query(query, (feature_id,))
        return results[0] if results else None
    
    def create_feature(self, feature_data: Dict) -> int:
        """创建新特征段"""
        query = """
            INSERT INTO t_pricing_fetures
            (序号, feture_id, project_id, 特征段名称, 线路长度, 风速, 覆冰, 回路数, 导线规格,
             平地, 丘陵, 山地, 高山, 峻岭, 泥沼, 河网, 组价状态, 创建时间, 更新时间)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        params = (
            feature_data.get('序号'),
            feature_data.get('序号'),  # 使用序号作为feture_id
            feature_data.get('project_id'),
            feature_data.get('特征段名称'),
            feature_data.get('线路长度'),
            feature_data.get('风速'),
            feature_data.get('覆冰'),
            feature_data.get('回路数'),
            feature_data.get('导线规格'),
            feature_data.get('平地', 0),
            feature_data.get('丘陵', 0),
            feature_data.get('山地', 0),
            feature_data.get('高山', 0),
            feature_data.get('峻岭', 0),
            feature_data.get('泥沼', 0),
            feature_data.get('河网', 0),
            feature_data.get('组价状态', '未组价'),
            feature_data.get('创建时间', now),
            feature_data.get('更新时间', now)
        )
        return self.db.execute_insert(query, params)
    
    def update_feature(self, feature_id: int, feature_data: Dict) -> int:
        """更新特征段信息"""
        query = """
            UPDATE t_pricing_fetures
            SET 特征段名称=?, 线路长度=?, 风速=?, 覆冰=?, 回路数=?, 导线规格=?,
                平地=?, 丘陵=?, 山地=?, 高山=?, 峻岭=?, 泥沼=?, 河网=?, 组价状态=?, 更新时间=?
            WHERE 序号 = ?
        """
        params = (
            feature_data.get('特征段名称'),
            feature_data.get('线路长度'),
            feature_data.get('风速'),
            feature_data.get('覆冰'),
            feature_data.get('回路数'),
            feature_data.get('导线规格'),
            feature_data.get('平地', 0),
            feature_data.get('丘陵', 0),
            feature_data.get('山地', 0),
            feature_data.get('高山', 0),
            feature_data.get('峻岭', 0),
            feature_data.get('泥沼', 0),
            feature_data.get('河网', 0),
            feature_data.get('组价状态', '未组价'),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            feature_id
        )
        return self.db.execute_update(query, params)
    
    def delete_feature(self, feature_id: int) -> int:
        """删除特征段"""
        query = "DELETE FROM t_pricing_fetures WHERE 序号 = ?"
        return self.db.execute_update(query, (feature_id,))
    
    def get_next_xuhao(self) -> int:
        """获取下一个序号"""
        query = "SELECT MAX(序号) as max_xuhao FROM t_pricing_fetures"
        results = self.db.execute_query(query)
        max_xuhao = results[0]['max_xuhao'] if results and results[0]['max_xuhao'] else 0
        return max_xuhao + 1

class PricingResultDAO:
    """组价结果数据访问对象"""
    
    def __init__(self):
        self.db = QuickPricingDB()
    
    def save_fen_result(self, result_data: Dict) -> int:
        """保存分气象区组价结果"""
        query = """
            INSERT INTO t_pricing_feture_result_fen 
            (feature_id, project_id, 特征段名称, 计算数据)
            VALUES (?, ?, ?, ?)
        """
        params = (
            result_data.get('feature_id'),
            result_data.get('project_id'),
            result_data.get('特征段名称'),
            result_data.get('计算数据')
        )
        return self.db.execute_insert(query, params)
    
    def save_he_result(self, result_data: Dict) -> int:
        """保存合并气象区组价结果"""
        query = """
            INSERT INTO t_pricing_feture_result_he 
            (feature_id, project_id, 特征段名称, 计算数据)
            VALUES (?, ?, ?, ?)
        """
        params = (
            result_data.get('feature_id'),
            result_data.get('project_id'),
            result_data.get('特征段名称'),
            result_data.get('计算数据')
        )
        return self.db.execute_insert(query, params)
