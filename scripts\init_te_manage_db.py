#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速组价模块数据库初始化脚本
创建 te_manage.db 数据库和相关表结构
"""

import os
import sqlite3
from datetime import datetime

def create_te_manage_database():
    """创建快速组价管理数据库和表结构"""
    db_path = os.path.join('data', 'te_manage.db')
    
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 创建工程管理表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_pricing_projects (
                序号 INTEGER PRIMARY KEY,
                project_id INTEGER UNIQUE NOT NULL,
                工程名称 TEXT NOT NULL,
                电压等级 TEXT,
                线路总长度 REAL,
                特征段数量 INTEGER DEFAULT 0,
                组价状态 TEXT NOT NULL DEFAULT '未组价',
                创建时间 DATETIME,
                组价时间 DATETIME
            )
        ''')
        
        # 2. 创建特征段管理表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_pricing_fetures (
                序号 INTEGER PRIMARY KEY,
                project_id INTEGER NOT NULL,
                feture_id INTEGER UNIQUE NOT NULL,
                特征段名称 TEXT NOT NULL,
                线路长度 REAL,
                风速 REAL,
                覆冰 REAL,
                回路数 TEXT,
                导线规格 TEXT,
                平地 REAL DEFAULT 0,
                丘陵 REAL DEFAULT 0,
                山地 REAL DEFAULT 0,
                高山 REAL DEFAULT 0,
                峻岭 REAL DEFAULT 0,
                泥沼 REAL DEFAULT 0,
                河网 REAL DEFAULT 0,
                组价状态 TEXT NOT NULL DEFAULT '未组价',
                创建时间 DATETIME,
                更新时间 DATETIME,
                FOREIGN KEY (project_id) REFERENCES t_pricing_projects(project_id)
            )
        ''')
        
        # 3. 创建分气象区组价结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_pricing_feture_result_fen (
                id INTEGER PRIMARY KEY,
                feature_id INTEGER NOT NULL,
                project_id INTEGER NOT NULL,
                特征段名称 TEXT NOT NULL,
                计算数据 TEXT NOT NULL,
                创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (feature_id) REFERENCES t_pricing_fetures(feture_id),
                FOREIGN KEY (project_id) REFERENCES t_pricing_projects(project_id)
            )
        ''')
        
        # 4. 创建合并气象区组价结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_pricing_feture_result_he (
                id INTEGER PRIMARY KEY,
                feature_id INTEGER NOT NULL,
                project_id INTEGER NOT NULL,
                特征段名称 TEXT NOT NULL,
                计算数据 TEXT NOT NULL,
                创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (feature_id) REFERENCES t_pricing_fetures(feature_id),
                FOREIGN KEY (project_id) REFERENCES t_pricing_projects(project_id)
            )
        ''')
        
        # 创建索引提高查询性能
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_projects_project_id ON t_pricing_projects(project_id)',
            'CREATE INDEX IF NOT EXISTS idx_fetures_project_id ON t_pricing_fetures(project_id)',
            'CREATE INDEX IF NOT EXISTS idx_result_fen_feature_id ON t_pricing_feture_result_fen(feature_id)',
            'CREATE INDEX IF NOT EXISTS idx_result_he_feature_id ON t_pricing_feture_result_he(feature_id)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        print(f"✅ 数据库创建成功: {db_path}")
        print("✅ 表结构创建完成:")
        print("   - t_pricing_projects (工程管理表)")
        print("   - t_pricing_fetures (特征段管理表)")
        print("   - t_pricing_feture_result_fen (分气象区组价结果表)")
        print("   - t_pricing_feture_result_he (合并气象区组价结果表)")
        print("✅ 索引创建完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def verify_database():
    """验证数据库表结构"""
    db_path = os.path.join('data', 'te_manage.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            't_pricing_projects',
            't_pricing_fetures', 
            't_pricing_feture_result_fen',
            't_pricing_feture_result_he'
        ]
        
        print("\n📋 数据库验证结果:")
        for table in expected_tables:
            if table in tables:
                print(f"   ✅ {table}")
            else:
                print(f"   ❌ {table} (缺失)")
        
        return all(table in tables for table in expected_tables)
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 开始创建快速组价管理数据库...")
    
    if create_te_manage_database():
        if verify_database():
            print("\n🎉 数据库初始化完成！")
        else:
            print("\n⚠️ 数据库验证失败，请检查表结构")
    else:
        print("\n💥 数据库初始化失败！")
