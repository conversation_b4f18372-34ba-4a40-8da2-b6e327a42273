// 指标管理相关的JavaScript代码
let currentProjectId = null;
let currentSectionId = null;

// 初始化选择ID数组，用于保存选中的指标
let selectedBentiIds = [];
let selectedQitaIds = [];

// 组价计算进度显示配置
const CALCULATION_PROGRESS_CONFIG = {
    stepInterval: 2000,  // 每步间隔时间（毫秒），默认2秒
    showProgress: true   // 是否显示进度提示
};

// 设置组价计算进度显示间隔时间（毫秒）
function setCalculationProgressInterval(milliseconds) {
    CALCULATION_PROGRESS_CONFIG.stepInterval = milliseconds;
    console.log(`组价计算进度显示间隔已设置为: ${milliseconds}ms (${milliseconds/1000}秒)`);
}

// 开启/关闭组价计算进度显示
function toggleCalculationProgress(enabled) {
    CALCULATION_PROGRESS_CONFIG.showProgress = enabled;
    console.log(`组价计算进度显示已${enabled ? '开启' : '关闭'}`);
}

// 演示数据
const demoData = {
    benti: [
        {
            "序号": 1,
            "工程名称": "某500kV输变电工程",
            "线路工程": "500kV某线路工程",
            "线路总长度": 50.5,
            "回路数": "双回路",
            "风速": 35,
            "覆冰": 15,
            "导线规格": "4×JL/G1A-400/35",
            "平地": 30,
            "丘陵": 40,
            "山地": 20,
            "高山": 10,
            "峻岭": 0,
            "泥沼": 0,
            "河网": 0,
            "人力运距": 2.5,
            "汽车运距": 15,
            "铁塔基数": 120,
            "直线塔": 90,
            "耐张塔": 30,
            "耐张比例": 25,
            "导线": 150,
            "塔材": 800,
            "基础钢材": 60,
            "地脚螺栓和插入式角钢": 20,
            "挂线金具": 25,
            "导线间隔棒": 240,
            "防振锤": 480,
            "导线防振锤": 360,
            "地线防振锤": 120,
            "合成复合绝缘子": 720,
            "玻璃绝缘子盘式绝缘子": 0,
            "硬跳": 120,
            "现浇混凝土": 3600,
            "灌柱桩基础混凝土": 1200,
            "基础护壁": 180,
            "基础垫层": 120,
            "钻孔灌注桩深度": 1800,
            "护坡挡土墙": 240,
            "土方量": 12000,
            "基坑土方非机械": 3600,
            "基坑土方机械": 8400,
            "接地槽": 360,
            "排水沟": 240,
            "尖峰基面": 120,
            "本体工程": 8000,
            "基础工程": 2400,
            "杆塔工程": 3200,
            "接地工程": 400,
            "架线工程": 1600,
            "附件工程": 240,
            "辅助工程": 160
        },
        {
            "序号": 2,
            "工程名称": "某220kV输变电工程",
            "线路工程": "220kV某线路工程",
            "线路总长度": 30.2,
            "回路数": "双回路",
            "风速": 30,
            "覆冰": 10,
            "导线规格": "2×JL/G1A-300/25",
            "平地": 50,
            "丘陵": 30,
            "山地": 20,
            "高山": 0,
            "峻岭": 0,
            "泥沼": 0,
            "河网": 0,
            "人力运距": 2,
            "汽车运距": 12,
            "铁塔基数": 80,
            "直线塔": 64,
            "耐张塔": 16,
            "耐张比例": 20,
            "导线": 90,
            "塔材": 480,
            "基础钢材": 36,
            "地脚螺栓和插入式角钢": 12,
            "挂线金具": 15,
            "导线间隔棒": 160,
            "防振锤": 320,
            "导线防振锤": 240,
            "地线防振锤": 80,
            "合成复合绝缘子": 480,
            "玻璃绝缘子盘式绝缘子": 0,
            "硬跳": 80,
            "现浇混凝土": 2400,
            "灌柱桩基础混凝土": 800,
            "基础护壁": 120,
            "基础垫层": 80,
            "钻孔灌注桩深度": 1200,
            "护坡挡土墙": 160,
            "土方量": 8000,
            "基坑土方非机械": 2400,
            "基坑土方机械": 5600,
            "接地槽": 240,
            "排水沟": 160,
            "尖峰基面": 80,
            "本体工程": 5000,
            "基础工程": 1500,
            "杆塔工程": 2000,
            "接地工程": 250,
            "架线工程": 1000,
            "附件工程": 150,
            "辅助工程": 100
        }
    ],
    qita: [
        {
            "序号": 1,
            "工程名称": "某500kV输变电工程",
            "线路工程": "500kV某线路工程",
            "合并气象区总长度": 50.5,
            "项目建设管理费": 400,
            "项目法人管理费": 200,
            "招标费": 80,
            "工程监理费": 160,
            "施工过程造价咨询及竣工结算审核费": 40,
            "工程保险费": 120,
            "项目建设技术服务费": 240,
            "项目前期工作费": 320,
            "勘察设计费": 480,
            "勘察费": 160,
            "设计费": 320,
            "基本设计费": 240,
            "其他设计费": 80,
            "设计文件评审费": 40,
            "可行性研究文件评审费": 16,
            "初步设计文件评审费": 12,
            "施工图文件评审费": 12,
            "工程建设检测费": 80,
            "电力工程质量检测费": 60,
            "桩基检测费": 20,
            "电力工程技术经济标准编制费": 40,
            "生产准备费": 160,
            "管理车辆购置费": 80,
            "工器具及办公家具购置费": 40,
            "生产职工培训及提前进场费": 32,
            "专业爆破服务费": 8
        },
        {
            "序号": 2,
            "工程名称": "某220kV输变电工程",
            "线路工程": "220kV某线路工程",
            "合并气象区总长度": 30.2,
            "项目建设管理费": 240,
            "项目法人管理费": 120,
            "招标费": 48,
            "工程监理费": 96,
            "施工过程造价咨询及竣工结算审核费": 24,
            "工程保险费": 72,
            "项目建设技术服务费": 144,
            "项目前期工作费": 192,
            "勘察设计费": 288,
            "勘察费": 96,
            "设计费": 192,
            "基本设计费": 144,
            "其他设计费": 48,
            "设计文件评审费": 24,
            "可行性研究文件评审费": 10,
            "初步设计文件评审费": 7,
            "施工图文件评审费": 7,
            "工程建设检测费": 48,
            "电力工程质量检测费": 36,
            "桩基检测费": 12,
            "电力工程技术经济标准编制费": 24,
            "生产准备费": 96,
            "管理车辆购置费": 48,
            "工器具及办公家具购置费": 24,
            "生产职工培训及提前进场费": 19,
            "专业爆破服务费": 5
        }
    ]
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('section_pricingSum.js 页面加载完成');
    // 初始化表格数据
    initTableData();
    
    // 确保标题显示
    const pageTitle = document.querySelector('.pricing-sp-page-title');
    if (pageTitle) {
        pageTitle.style.display = 'block';
        pageTitle.style.visibility = 'visible';
        pageTitle.style.opacity = '1';
        pageTitle.style.fontWeight = 'bold';
        console.log('已设置页面标题样式');
    }
});

// 初始化函数，由iframe父页面调用
function initIndicatorSelect(projectId, sectionId) {
    console.log('initIndicatorSelect被调用，工程ID:', projectId, '特征段ID:', sectionId);
    
    currentProjectId = projectId;
    currentSectionId = sectionId;
    
    // 尝试从sessionStorage获取特征段数据
    try {
        const sectionData = JSON.parse(sessionStorage.getItem('selectedSectionData'));
        if (sectionData) {
            console.log('=== 从sessionStorage获取特征段数据 ===');
            console.log('完整数据:', sectionData);
            console.log('气象区字段值:', sectionData.气象区);
            console.log('气象区字段类型:', typeof sectionData.气象区);

            // 验证气象区数据
            const weatherZone = validateWeatherZone(sectionData.气象区);
            console.log('验证后的气象区值:', weatherZone);

            // 更新UI
            document.getElementById('sectionName').textContent = sectionData.特征段名称 || '-';
            document.getElementById('lineLength').textContent = sectionData.线路长度 ? sectionData.线路长度 + ' km' : '-';
            document.getElementById('windSpeed').textContent = sectionData.风速 ? sectionData.风速 + ' m/s' : '-';
            document.getElementById('iceThickness').textContent = sectionData.覆冰 ? sectionData.覆冰 + ' mm' : '-';
            document.getElementById('circuitCount').textContent = sectionData.回路数 || '-';
            document.getElementById('wireSpec').textContent = sectionData.导线型号 || sectionData.导线规格 || '-';

            // 根据气象区控制tab显示
            controlTabsByWeatherZone(weatherZone);
        } else {
            // console.log('sessionStorage中没有特征段数据，尝试从API加载');
            loadSectionInfo();
        }
    } catch (error) {
        console.error('解析sessionStorage数据失败:', error);
        loadSectionInfo();
    }
    
    // 初始化表格数据
    initTableData();
    
    // 加载指标数据
    loadIndicators();
}

// 验证气象区数据
function validateWeatherZone(weatherZone) {
    console.log('验证气象区数据:', weatherZone);

    // 有效的气象区值
    const validValues = ['分', '合并'];

    // 如果值为空或未定义，返回默认值
    if (!weatherZone || weatherZone === null || weatherZone === undefined) {
        console.log('气象区值为空，使用默认值"分"');
        return '分';
    }

    // 转换为字符串并去除空格
    const cleanValue = String(weatherZone).trim();

    // 检查是否为有效值
    if (validValues.includes(cleanValue)) {
        console.log('气象区值有效:', cleanValue);
        return cleanValue;
    }

    // 如果不是有效值，返回默认值并记录警告
    console.warn('无效的气象区值:', weatherZone, '使用默认值"分"');
    return '分';
}

// 根据气象区控制tab页显示
function controlTabsByWeatherZone(weatherZone) {
    console.log('=== 开始根据气象区控制tab显示 ===');
    console.log('气象区值:', weatherZone);

    // 定义需要控制的tab区域
    const tabAreas = ['base', 'history', 'price'];

    // 为每个区域分别控制tab显示
    tabAreas.forEach(area => {
        console.log(`处理${area}区域的tab控制`);

        // 获取特定区域的tab按钮 - 使用更精确的选择器
        const bentiTabBtn = document.querySelector(`button[onclick="switchTab('benti', '${area}')"]`);
        const qitaTabBtn = document.querySelector(`button[onclick="switchTab('qita', '${area}')"]`);

        // 获取特定区域的面板
        const bentiPanel = document.getElementById(`bentiPanel-${area}`);
        const qitaPanel = document.getElementById(`qitaPanel-${area}`);

        console.log(`${area}区域元素查找结果:`, {
            bentiTabBtn: !!bentiTabBtn,
            qitaTabBtn: !!qitaTabBtn,
            bentiPanel: !!bentiPanel,
            qitaPanel: !!qitaPanel
        });

        if (bentiTabBtn) console.log(`${area}区域本体按钮onclick:`, bentiTabBtn.getAttribute('onclick'));
        if (qitaTabBtn) console.log(`${area}区域其他按钮onclick:`, qitaTabBtn.getAttribute('onclick'));

        if (weatherZone === '分') {
            // 气象区为"分"时，只显示本体费用指标tab
            console.log(`${area}区域: 气象区为"分"，只显示本体费用指标tab`);

            // 显示本体费用指标tab按钮和面板
            if (bentiTabBtn) {
                bentiTabBtn.style.display = 'inline-block';
                bentiTabBtn.classList.add('active');
                console.log(`${area}区域: 显示本体费用指标tab按钮`);
            }
            if (bentiPanel) {
                bentiPanel.style.display = 'block';
                console.log(`${area}区域: 显示本体费用指标面板`);
            }

            // 隐藏其他费用指标tab按钮和面板
            if (qitaTabBtn) {
                qitaTabBtn.style.display = 'none';
                qitaTabBtn.classList.remove('active');
                console.log(`${area}区域: 隐藏其他费用指标tab按钮`);
            }
            if (qitaPanel) {
                qitaPanel.style.display = 'none';
                console.log(`${area}区域: 隐藏其他费用指标面板`);
            }

        } else if (weatherZone === '合并') {
            // 气象区为"合并"时，只显示其他费用指标tab
            console.log(`${area}区域: 气象区为"合并"，只显示其他费用指标tab`);

            // 隐藏本体费用指标tab按钮和面板
            if (bentiTabBtn) {
                bentiTabBtn.style.display = 'none';
                bentiTabBtn.classList.remove('active');
                console.log(`${area}区域: 隐藏本体费用指标tab按钮`);
            }
            if (bentiPanel) {
                bentiPanel.style.display = 'none';
                console.log(`${area}区域: 隐藏本体费用指标面板`);
            }

            // 显示其他费用指标tab按钮和面板
            if (qitaTabBtn) {
                qitaTabBtn.style.display = 'inline-block';
                qitaTabBtn.classList.add('active');
                console.log(`${area}区域: 显示其他费用指标tab按钮`);
            }
            if (qitaPanel) {
                qitaPanel.style.display = 'block';
                console.log(`${area}区域: 显示其他费用指标面板`);
            }
        }
    });

    console.log('=== tab控制完成 ===');

    // 更新页面上的气象区状态显示（调试用）
    updateWeatherZoneDisplay(weatherZone);
}

// 更新页面上的气象区状态显示
function updateWeatherZoneDisplay(weatherZone) {
    const displayElement = document.getElementById('currentWeatherZone');
    if (displayElement) {
        displayElement.textContent = weatherZone;
        displayElement.style.color = weatherZone === '分' ? '#28a745' : '#dc3545';
    }
}

// 调试模式相关变量
let debugMode = false;

// 切换调试模式
function toggleDebugMode() {
    debugMode = !debugMode;
    const toggleBtn = document.getElementById('toggleDebugMode');
    const debugControls = document.getElementById('debugControls');

    if (debugMode) {
        toggleBtn.textContent = '关闭';
        toggleBtn.style.background = '#dc3545';
        debugControls.style.display = 'block';
        // console.log('调试模式已开启');
    } else {
        toggleBtn.textContent = '开启';
        toggleBtn.style.background = '#6c757d';
        debugControls.style.display = 'none';
        // console.log('调试模式已关闭');
    }
}

// 测试气象区功能
function testWeatherZone(weatherZone) {
    // 调试日志（生产环境可注释）
    // console.log('手动测试气象区功能:', weatherZone);

    // 直接调用tab控制函数
    controlTabsByWeatherZone(weatherZone);

    // 显示测试提示
    showToast(`正在测试气象区"${weatherZone}"的tab显示效果`, 'info');
}

// 导出调试函数到全局
window.toggleDebugMode = toggleDebugMode;
window.testWeatherZone = testWeatherZone;

/*
 * 调试功能说明：
 * 1. 如需启用调试面板，请在HTML中将 weatherZoneDebugPanel 的 style="display: none;" 改为 style="display: block;"
 * 2. 如需启用详细日志，请取消注释相关的 console.log 语句
 * 3. 调试面板提供手动测试不同气象区的tab显示效果
 * 4. 生产环境建议保持调试面板隐藏，但保留功能代码以便后续调试
 */

// 初始化表格数据
function initTableData() {
    console.log('初始化表格数据');

    // 添加本体费用基准指标的默认行（按新的顺序和名称）
    const bentiBody = document.getElementById('bentiBaseTableBody');
    if (bentiBody) {
        const rows = [
            '指标单公里基准a',
            '指标单价b',
            '指标单公里组价c=a*b',
            '指标总量d=a*长度',
            '总价e=c*长度'
        ];
        // 本体指标实际为35个，需要常见35列，除开价格|数量列
        // 本体指标改成28个，
        bentiBody.innerHTML = rows.map(row => `
            <tr>
                <td>${row}</td>
                ${Array(28).fill('<td></td>').join('')}
            </tr>
        `).join('');
    }

    // 添加其他费用基准指标的默认行（简化为2行）
    const qitaBody = document.getElementById('qitaBaseTableBody');
    if (qitaBody) {
        // 其他费用指标实际为26个，需要常见26列，除开价格|数量列
        const rows = ['指标单公里基准I', '总价J=I*长度'];
        qitaBody.innerHTML = rows.map(row => `
            <tr>
                <td>${row}</td>
                ${Array(26).fill('<td></td>').join('')}
            </tr>
        `).join('');
    }

    // 初始化本体工程数据表格
    const bentiProjectBody = document.getElementById('bentiProjectTableBody');
    if (bentiProjectBody) {
        bentiProjectBody.innerHTML = `
            <tr>
                <td>价格</td>
                <td>5.22</td>
                <td>5.35</td>
                <td>160.63</td>
            </tr>
        `;
    }
}

// 切换指标类型标签
function switchTab(type, section) {
    const buttons = document.querySelectorAll(`.pricing-sp-indicator-tabs button`);
    buttons.forEach(btn => {
        if (btn.getAttribute('onclick') && btn.getAttribute('onclick').includes(`'${type}'`) && 
            btn.getAttribute('onclick').includes(`'${section}'`)) {
            btn.classList.add('active');
        } else if (btn.getAttribute('onclick') && btn.getAttribute('onclick').includes(`'${section}'`)) {
            btn.classList.remove('active');
        }
    });
    
    // 切换面板显示
    document.getElementById(`bentiPanel-${section}`).style.display = type === 'benti' ? 'block' : 'none';
    document.getElementById(`qitaPanel-${section}`).style.display = type === 'benti' ? 'none' : 'block';
}

// 显示历史指标查询模态框
function showHistoryIndicatorModal() {
    console.log('显示历史指标查询模态框');
    const modal = document.getElementById('historyIndicatorModal');
    if (modal) {
        modal.classList.add('show');
    } else {
        console.error('找不到历史指标查询模态框');
    }
    
    // 清空已选择的指标
    selectedBentiIds = [];
    selectedQitaIds = [];
    
    // 清空表格数据
    if (document.getElementById('bentiTableBody')) {
        document.getElementById('bentiTableBody').innerHTML = '';
    }
    if (document.getElementById('qitaTableBody')) {
        document.getElementById('qitaTableBody').innerHTML = '';
    }
    
    // 显示空数据提示
    if (document.getElementById('bentiEmptyData')) {
        document.getElementById('bentiEmptyData').style.display = 'block';
    }
    if (document.getElementById('qitaEmptyData')) {
        document.getElementById('qitaEmptyData').style.display = 'block';
    }
}

// 隐藏历史指标查询模态框
function hideHistoryIndicatorModal() {
    console.log('隐藏历史指标查询模态框');
    const modal = document.getElementById('historyIndicatorModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

// 确认创建基准指标
function confirmCreateBaseIndicator() {
    console.log('确认创建基准指标函数被调用');
    console.log('selectedBentiIds:', selectedBentiIds);
    console.log('selectedQitaIds:', selectedQitaIds);
    
    // 模拟选中指标，用于测试
    if (selectedBentiIds.length === 0 && selectedQitaIds.length === 0) {
        console.log('没有选中任何指标，添加测试数据');
        selectedBentiIds = [1, 2]; // 添加测试数据
        console.log('添加测试数据后 selectedBentiIds:', selectedBentiIds);
    }
    
    // 如果没有选择任何指标，显示错误提示
    if (selectedBentiIds.length === 0 && selectedQitaIds.length === 0) {
        showToast('请至少选择一条历史指标数据', 'error');
        return;
    }
    
    // 显示确认对话框
    showModal();
}

// 显示确认对话框
function showModal() {
    console.log('显示确认对话框');
    const confirmModal = document.getElementById('confirmModal');
    if (confirmModal) {
        // 先将模态框移至body的最后位置，确保它在DOM顺序上最后
        document.body.appendChild(confirmModal);
        
        // 直接设置display属性
        confirmModal.style.display = 'flex';
        confirmModal.classList.add('show');
        
        // 添加记录到控制台
        console.log('确认对话框已显示', confirmModal);
    } else {
        console.error('找不到确认对话框元素');
    }
}

// 隐藏确认对话框
function hideModal() {
    console.log('隐藏确认对话框');
    const confirmModal = document.getElementById('confirmModal');
    if (confirmModal) {
        confirmModal.style.display = 'none';
        confirmModal.classList.remove('show');
        console.log('确认对话框已隐藏');
    } else {
        console.error('找不到确认对话框元素');
    }
}

// 确认模态框操作
function confirmModal() {
    console.log('确认模态框操作');
    hideModal();
    
    // 隐藏历史指标查询弹出层
    hideHistoryIndicatorModal();
    
    // 生成加权平均的基准指标数据
    generateWeightedAverageBaseIndicatorData();
    
    // 显示提示信息
    showToast('基准指标创建成功', 'success');
}

// 生成加权平均的基准指标数据
function generateWeightedAverageBaseIndicatorData() {
    // 获取本体费用基准指标表格的第一行（指标单公里基准a）
    const bentiBaseRow = document.querySelector('#bentiBaseTableBody tr:first-child');

    if (bentiBaseRow) {
        // 跳过第一列（数量列），填充随机数据，模拟加权平均结果
        for (let i = 1; i < bentiBaseRow.cells.length; i++) {
            // 生成2-8之间的随机数，模拟加权平均结果
            const weightedValue = (Math.random() * 6 + 2).toFixed(2);
            bentiBaseRow.cells[i].textContent = weightedValue;
        }
    }

    // 获取其他费用基准指标表格的第一行（指标单公里基准I）
    const qitaBaseRow = document.querySelector('#qitaBaseTableBody tr:first-child');
    if (qitaBaseRow) {
        // 跳过第一列（数量列），填充随机数据，模拟加权平均结果
        for (let i = 1; i < qitaBaseRow.cells.length; i++) {
            // 生成2-8之间的随机数，模拟加权平均结果
            const weightedValue = (Math.random() * 6 + 2).toFixed(2);
            qitaBaseRow.cells[i].textContent = weightedValue;
        }
    }

    // 清空其他行的数据
    clearOtherRowsData('bentiBaseTableBody');
    clearOtherRowsData('qitaBaseTableBody');
}

// 清空除第一行和第三行外的其他行数据
function clearOtherRowsData(tableId) {
    const tbody = document.getElementById(tableId);
    if (!tbody) return;

    if (tableId === 'bentiBaseTableBody') {
        // 本体费用指标：清空第2、4、5行（保留第1行基准a和第3行单价b）
        for (let rowIndex = 1; rowIndex < tbody.rows.length; rowIndex++) {
            if (rowIndex !== 2) { // 不清空第3行（索引为2）的单价数据
                const row = tbody.rows[rowIndex];
                for (let i = 1; i < row.cells.length; i++) {
                    row.cells[i].textContent = '';
                }
            }
        }
    } else if (tableId === 'qitaBaseTableBody') {
        // 其他费用指标：清空第2行（保留第1行基准I）
        for (let rowIndex = 1; rowIndex < tbody.rows.length; rowIndex++) {
            const row = tbody.rows[rowIndex];
            for (let i = 1; i < row.cells.length; i++) {
                row.cells[i].textContent = '';
            }
        }
    }
}

// 编辑基准指标
function editBaseIndicator() {
    console.log('编辑基准指标');
    const editButton = document.querySelector('.pricing-sp-btn-edit');
    // 编辑第一行（指标单公里基准a）
    const bentiBaseCells = document.querySelectorAll('#bentiBaseTableBody tr:first-child td:not(:first-child)');
    const qitaBaseCells = document.querySelectorAll('#qitaBaseTableBody tr:first-child td:not(:first-child)');

    // 如果当前是编辑模式，则保存
    if (editButton.textContent === '保存') {
        // 移除可编辑状态
        bentiBaseCells.forEach(cell => {
            cell.contentEditable = 'false';
            cell.classList.remove('editable-cell');
        });

        qitaBaseCells.forEach(cell => {
            cell.contentEditable = 'false';
            cell.classList.remove('editable-cell');
        });

        // 更改按钮文本
        editButton.textContent = '基准指标编辑';

        // 显示保存成功提示
        showToast('基准指标保存成功', 'success');
    } else {
        // 使单元格可编辑
        bentiBaseCells.forEach(cell => {
            cell.contentEditable = 'true';
            cell.classList.add('editable-cell');

            // 如果单元格为空，添加随机数据
            if (!cell.textContent.trim()) {
                cell.textContent = (Math.random() * 10).toFixed(2);
            }
        });

        qitaBaseCells.forEach(cell => {
            cell.contentEditable = 'true';
            cell.classList.add('editable-cell');

            // 如果单元格为空，添加随机数据
            if (!cell.textContent.trim()) {
                cell.textContent = (Math.random() * 10).toFixed(2);
            }
        });

        // 更改按钮文本
        editButton.textContent = '保存';
    }
}



// 单价管理
function managePrice() {
    console.log('单价管理');
    
    // 显示单价管理模态框
    showPriceModal();
}

// 显示单价管理模态框
function showPriceModal() {
    console.log('显示单价管理模态框');
    const modal = document.getElementById('priceManageModal');
    if (modal) {
        // 先将模态框移至body的最后位置，确保它在DOM顺序上最后
        document.body.appendChild(modal);
        
        // 直接设置display属性
        modal.style.display = 'flex';
        modal.classList.add('show');
        
        // 加载单价数据
        loadPriceData();
    } else {
        console.error('找不到单价管理模态框');
    }
}

// 隐藏单价管理模态框
function hidePriceModal() {
    console.log('隐藏单价管理模态框');
    const modal = document.getElementById('priceManageModal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
    }
}

// 加载单价数据到模态框
function loadPriceData() {
    console.log('加载单价数据');
    
    // 获取表头数据
    const bentiHeaders = getTableHeaders('bentiPanel-base');
    const qitaHeaders = getTableHeaders('qitaPanel-base');
    
    // 获取当前单价数据（第二行）
    const bentiPrices = getCurrentPrices('bentiBaseTableBody');
    const qitaPrices = getCurrentPrices('qitaBaseTableBody');
    
    // 填充单价表格
    renderPriceTable('bentiPriceTableBody', bentiHeaders, bentiPrices);
    renderPriceTable('qitaPriceTableBody', qitaHeaders, qitaPrices);
}

// 获取表格表头数据
function getTableHeaders(panelId) {
    const headers = [];

    if (panelId === 'bentiPanel-base') {
        // 只获取第一个表格的表头，不包括本体工程表格
        const headerCells = document.querySelectorAll(`#${panelId} .pricing-sp-table-container:first-child thead th`);

        // 跳过第一个单元格（数量|价格）
        for (let i = 1; i < headerCells.length; i++) {
            headers.push(headerCells[i].textContent);
        }
    } else {
        // 其他面板正常获取
        const headerCells = document.querySelectorAll(`#${panelId} thead th`);

        // 跳过第一个单元格（数量|价格）
        for (let i = 1; i < headerCells.length; i++) {
            headers.push(headerCells[i].textContent);
        }
    }

    return headers;
}

// 获取当前单价数据
function getCurrentPrices(tableId) {
    const prices = [];

    if (tableId === 'bentiBaseTableBody') {
        // 本体费用指标：单价在第2行（指标单价b）
        const row = document.querySelector(`#${tableId} tr:nth-child(2)`);
        if (row) {
            // 跳过第一个单元格（指标单价b）
            for (let i = 1; i < row.cells.length; i++) {
                prices.push(row.cells[i].textContent || '');
            }
        }
    } else if (tableId === 'qitaBaseTableBody') {
        // 其他费用指标：没有单独的单价行，返回空数组
        // 因为其他费用指标只有2行：基准I和总价J
        return [];
    }

    return prices;
}

// 渲染单价表格
function renderPriceTable(tableId, headers, prices) {
    const tbody = document.getElementById(tableId);
    if (!tbody) {
        console.error(`找不到${tableId}元素`);
        return;
    }
    
    tbody.innerHTML = '';
    
    for (let i = 0; i < headers.length; i++) {
        const tr = document.createElement('tr');
        
        // 提取指标名称，去掉括号部分
        const headerText = headers[i];
        const nameText = headerText.replace(/\([^)]+\)/, '').trim();
        
        // 指标名称单元格
        const nameCell = document.createElement('td');
        nameCell.textContent = nameText;
        tr.appendChild(nameCell);
        
        // 单位单元格
        const unitCell = document.createElement('td');
        unitCell.textContent = getUnitFromHeader(headerText);
        tr.appendChild(unitCell);
        
        // 单价单元格
        const priceCell = document.createElement('td');
        const priceInput = document.createElement('input');
        priceInput.type = 'number';
        priceInput.className = 'pricing-shared-input';
        priceInput.value = prices[i] || '';
        priceInput.min = '0';
        priceInput.step = '1';
        
        // 如果单元格为空，生成随机价格
        if (!prices[i]) {
            const randomPrice = Math.floor(Math.random() * 9000) + 1000;
            priceInput.value = randomPrice;
        }
        
        priceCell.appendChild(priceInput);
        tr.appendChild(priceCell);
        
        tbody.appendChild(tr);
    }
}

// 从表头文本中提取单位
function getUnitFromHeader(headerText) {
    // 使用正则表达式匹配括号中的单位，如"铁塔基数(基)"中的"基"
    const match = headerText.match(/\(([^)]+)\)/);
    if (match && match[1]) {
        return match[1];
    }
    
    // 如果没有找到匹配的单位，返回默认值
    return '-';
}

// 保存单价数据
function savePriceData() {
    console.log('保存单价数据');

    // 获取本体费用单价数据
    const bentiPrices = document.querySelectorAll('#bentiPriceTableBody input[type="number"]');
    const bentiRow = document.querySelector('#bentiBaseTableBody tr:nth-child(2)'); // 指标单价b行

    console.log('找到的单价输入框数量:', bentiPrices.length);
    console.log('找到的目标行:', bentiRow);

    if (!bentiRow) {
        console.error('未找到目标行：#bentiBaseTableBody tr:nth-child(2)');
        showToast('保存失败：未找到目标行', 'error');
        return;
    }

    if (bentiPrices.length === 0) {
        console.error('未找到单价输入框');
        showToast('保存失败：未找到单价输入框', 'error');
        return;
    }

    console.log('目标行的单元格数量:', bentiRow.cells.length);
    console.log('单价输入框数量:', bentiPrices.length);

    // 检查单元格数量是否足够（需要跳过第一个标签列，所以需要 +1）
    if (bentiRow.cells.length < bentiPrices.length + 1) {
        console.error('目标行单元格数量不足，需要:', bentiPrices.length + 1, '实际:', bentiRow.cells.length);
        showToast('保存失败：表格结构异常', 'error');
        return;
    }

    // 跳过第一个单元格（指标单价b），保存单价数据
    for (let i = 0; i < bentiPrices.length; i++) {
        if (i + 1 < bentiRow.cells.length) {
            bentiRow.cells[i + 1].textContent = bentiPrices[i].value;
            console.log(`保存第${i+1}列单价:`, bentiPrices[i].value);
        } else {
            console.error(`单元格索引超出范围: ${i + 1}`);
        }
    }

    // 显示保存成功提示
    showToast('单价数据保存成功', 'success');

    // 隐藏单价管理模态框
    hidePriceModal();
}

// 填充单价数据（演示用）
function fillPriceData() {
    const bentiRow = document.querySelector('#bentiBaseTableBody tr:nth-child(2)'); // 指标单价b行

    if (bentiRow) {
        for (let i = 1; i < bentiRow.cells.length; i++) {
            // 生成1000-10000之间的随机价格
            const randomPrice = Math.floor(Math.random() * 9000) + 1000;
            bentiRow.cells[i].textContent = randomPrice;
        }
    }

    // 其他费用指标不需要填充单价数据，因为已简化为2行
    console.log('其他费用指标已简化，不需要单独的单价数据');

    showToast('单价数据已更新', 'success');
}

// 组价计算
function calculatePrice() {
    console.log('组价计算');

    if (!CALCULATION_PROGRESS_CONFIG.showProgress) {
        // 如果不显示进度，直接执行计算
        calculateGroupPrice('bentiBaseTableBody');
        calculateGroupPrice('qitaBaseTableBody');
        calculateBentiProjectData();
        showToast('组价计算完成', 'success');
        return;
    }

    // 设置默认的展示时间 ，2秒
    const interval = CALCULATION_PROGRESS_CONFIG.stepInterval;
    showToast('开始进行组价计算...', 'info');

    // 使用配置的时间间隔逐步显示计算过程
    setTimeout(() => {
        // 第一步：计算本体费用指标组价
        calculateGroupPriceWithProgress('bentiBaseTableBody');

        setTimeout(() => {
            // 第二步：计算其他费用指标组价
            calculateGroupPrice('qitaBaseTableBody');

            setTimeout(() => {
                // 第三步：计算本体工程数据表格
                calculateBentiProjectDataWithProgress();

                setTimeout(() => {
                    // 最终完成提示
                    showToast('所有组价计算完成！', 'success');

                    // 更新组价状态为已组价
                    updatePricingStatus();
                }, interval / 4); // 最后一步稍微快一点
            }, interval / 2); // 中间步骤稍微快一点
        }, interval / 2);
    }, interval / 4); // 开始稍微快一点
}

// 更新组价状态为已组价
async function updatePricingStatus() {
    if (!currentProjectId || !currentSectionId) {
        console.error('缺少工程ID或特征段ID，无法更新组价状态');
        return;
    }

    try {
        const response = await fetch(`/api/pricing/projects/${currentProjectId}/feature_sections/${currentSectionId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                组价状态: '已组价'
            })
        });

        if (response.ok) {
            console.log('组价状态更新成功');
            showToast('组价状态已更新为已组价', 'success');
        } else {
            console.error('组价状态更新失败');
            showToast('组价状态更新失败', 'error');
        }
    } catch (error) {
        console.error('更新组价状态时发生错误:', error);
        showToast('组价状态更新失败: ' + error.message, 'error');
    }
}

// 带进度提示的本体费用指标组价计算
function calculateGroupPriceWithProgress(tableId) {
    if (tableId !== 'bentiBaseTableBody') {
        calculateGroupPrice(tableId);
        return;
    }

    const tbody = document.getElementById(tableId);
    if (!tbody) return;

    const row1 = tbody.rows[0]; // 第一行（指标单公里基准a）
    const row2 = tbody.rows[1]; // 第二行（指标单价b）
    const row3 = tbody.rows[2]; // 第三行（指标单公里组价c=a*b）
    const row4 = tbody.rows[3]; // 第四行（指标总量d=a*长度）
    const row5 = tbody.rows[4]; // 第五行（总价e=c*长度）

    if (!row1 || !row2 || !row3 || !row4 || !row5) {
        console.error('本体费用指标表格行数不足');
        return;
    }

    console.log('开始计算本体费用指标组价');

    const interval = CALCULATION_PROGRESS_CONFIG.stepInterval;

    // 第一步：计算指标单公里组价c
    showToast('正在计算指标单公里组价c = a × b...', 'info');

    setTimeout(() => {
        // 跳过第一列（标签列），计算其他列
        for (let i = 1; i < row1.cells.length; i++) {
            const valueA = parseFloat(row1.cells[i].textContent) || 0; // 指标单公里基准a
            const valueB = parseFloat(row2.cells[i].textContent) || 0; // 指标单价b

            // 计算第3行：c = a * b（指标单公里组价）
            const valueC = valueA * valueB;
            row3.cells[i].textContent = valueC.toFixed(2);
        }

        showToast('指标单公里组价c 计算完成', 'success');

        // 第二步：计算指标总量d
        setTimeout(() => {
            showToast('正在计算指标总量d = a × 长度...', 'info');

            setTimeout(() => {
                for (let i = 1; i < row1.cells.length; i++) {
                    const valueA = parseFloat(row1.cells[i].textContent) || 0;
                    const valueD = valueA * 30;
                    row4.cells[i].textContent = valueD.toFixed(2);
                }

                showToast('指标总量d 计算完成', 'success');

                // 第三步：计算总价e
                setTimeout(() => {
                    showToast('正在计算总价e = c × 长度...', 'info');

                    setTimeout(() => {
                        for (let i = 1; i < row1.cells.length; i++) {
                            const valueC = parseFloat(row3.cells[i].textContent) || 0;
                            const valueE = valueC * 30;
                            row5.cells[i].textContent = valueE.toFixed(2);
                        }

                        showToast('总价e 计算完成', 'success');
                    }, interval / 2);
                }, interval);
            }, interval / 2);
        }, interval);
    }, interval / 2);
}

// 计算组价
function calculateGroupPrice(tableId) {
    const tbody = document.getElementById(tableId);
    if (!tbody) return;

    if (tableId === 'bentiBaseTableBody') {
        // 本体费用指标计算
        const row1 = tbody.rows[0]; // 第一行（指标单公里基准a）
        const row2 = tbody.rows[1]; // 第二行（指标单价b）
        const row3 = tbody.rows[2]; // 第三行（指标单公里组价c=a*b）
        const row4 = tbody.rows[3]; // 第四行（指标总量d=a*长度）
        const row5 = tbody.rows[4]; // 第五行（总价e=c*长度）

        if (!row1 || !row2 || !row3 || !row4 || !row5) {
            console.error('本体费用指标表格行数不足');
            return;
        }

        console.log('开始计算本体费用指标组价');

        // 跳过第一列（标签列），计算其他列
        for (let i = 1; i < row1.cells.length; i++) {
            const valueA = parseFloat(row1.cells[i].textContent) || 0; // 指标单公里基准a
            const valueB = parseFloat(row2.cells[i].textContent) || 0; // 指标单价b

            // 计算第3行：c = a * b（指标单公里组价）
            const valueC = valueA * valueB;
            row3.cells[i].textContent = valueC.toFixed(2);

            // 计算第4行：d = a * 长度（指标总量）
            const valueD = valueA * 30;
            row4.cells[i].textContent = valueD.toFixed(2);

            // 计算第5行：e = c * 长度（总价）
            const valueE = valueC * 30;
            row5.cells[i].textContent = valueE.toFixed(2);

            console.log(`第${i}列计算结果: a=${valueA}, b=${valueB}, c=${valueC}, d=${valueD}, e=${valueE}`);
        }
    } else if (tableId === 'qitaBaseTableBody') {
        // 其他费用指标计算
        const tbody = document.getElementById(tableId);
        if (!tbody) return;

        const row1 = tbody.rows[0]; // 第一行（指标单公里基准I）
        const row2 = tbody.rows[1]; // 第二行（总价J=I*长度）

        if (!row1 || !row2) return;

        // 跳过第一列（数量列），计算其他列：J = I * 30
        for (let i = 1; i < row1.cells.length; i++) {
            const valueI = parseFloat(row1.cells[i].textContent) || 0;
            const valueJ = valueI * 30;
            row2.cells[i].textContent = valueJ.toFixed(2);
        }
    }
}

// 带进度提示的本体工程数据表格计算
function calculateBentiProjectDataWithProgress() {
    const tbody = document.getElementById('bentiBaseTableBody');
    const projectTbody = document.getElementById('bentiProjectTableBody');

    if (!tbody || !projectTbody) return;

    const row3 = tbody.rows[2]; // 第三行（指标单公里组价c=a*b）

    if (!row3) return;

    const interval = CALCULATION_PROGRESS_CONFIG.stepInterval;

    // 第四步：计算指标单公里组价g
    showToast('正在计算指标单公里组价g = c(基础+塔材+导线) × 0.7/10000...', 'info');

    setTimeout(() => {
        // 获取基础、塔材、导线的组价数据
        // 根据表头，基础钢材在第8列，塔材在第7列，导线在第6列（索引从1开始，跳过第一列）
        const jichuIndex = 7;  // 基础钢材(t)
        const tacaiIndex = 6;  // 塔材(t)
        const daoxianIndex = 5; // 导线(t)

        const jichuValue = parseFloat(row3.cells[jichuIndex].textContent) || 0;
        const tacaiValue = parseFloat(row3.cells[tacaiIndex].textContent) || 0;
        const daoxianValue = parseFloat(row3.cells[daoxianIndex].textContent) || 0;

        // 计算 g = c(基础+塔材+导线) * 0.7 / 10000
        const gValue = (jichuValue + tacaiValue + daoxianValue) * 0.7 / 10000;

        // 更新本体工程表格的g值
        const projectRow = projectTbody.rows[0];
        if (projectRow) {
            projectRow.cells[2].textContent = gValue.toFixed(2); // g值
        }

        showToast('指标单公里组价g 计算完成', 'success');

        // 第五步：计算总价h
        setTimeout(() => {
            showToast('正在计算总价h = g × 长度...', 'info');

            setTimeout(() => {
                // 计算 h = g * 长度（30）
                const hValue = gValue * 30;

                // 更新本体工程表格的h值
                if (projectRow) {
                    projectRow.cells[3].textContent = hValue.toFixed(2); // h值
                }

                showToast('总价h 计算完成', 'success');
            }, interval / 2);
        }, interval);
    }, interval / 2);
}

// 计算本体工程数据表格（原版本，保留用于其他地方调用）
function calculateBentiProjectData() {
    const tbody = document.getElementById('bentiBaseTableBody');
    const projectTbody = document.getElementById('bentiProjectTableBody');

    if (!tbody || !projectTbody) return;

    const row3 = tbody.rows[2]; // 第三行（指标单公里组价c=a*b）

    if (!row3) return;

    // 获取基础、塔材、导线的组价数据
    const jichuIndex = 7;  // 基础钢材(t)
    const tacaiIndex = 6;  // 塔材(t)
    const daoxianIndex = 5; // 导线(t)

    const jichuValue = parseFloat(row3.cells[jichuIndex].textContent) || 0;
    const tacaiValue = parseFloat(row3.cells[tacaiIndex].textContent) || 0;
    const daoxianValue = parseFloat(row3.cells[daoxianIndex].textContent) || 0;

    // 计算 g = c(基础+塔材+导线) * 0.7 / 10000
    const gValue = (jichuValue + tacaiValue + daoxianValue) * 0.7 / 10000;

    // 计算 h = g * 长度（30）
    const hValue = gValue * 30;

    // 更新本体工程表格
    const projectRow = projectTbody.rows[0];
    if (projectRow) {
        projectRow.cells[2].textContent = gValue.toFixed(2); // g值
        projectRow.cells[3].textContent = hValue.toFixed(2); // h值
    }
}

// 从后端加载特征段信息
async function loadSectionInfo() {
    if (!currentProjectId || !currentSectionId) {
        console.error('缺少工程ID或特征段ID');
        return;
    }
    
    try {
        const response = await fetch(`/api/pricing/projects/${currentProjectId}/feature_sections/${currentSectionId}`);
        if (!response.ok) {
            throw new Error('加载特征段信息失败');
        }
        
        const data = await response.json();
        // 调试日志（生产环境可注释）
        // console.log('从API获取特征段数据:', data);
        // console.log('气象区字段值:', data.气象区);

        // 验证气象区数据
        const weatherZone = validateWeatherZone(data.气象区);

        // 更新UI
        document.getElementById('sectionName').textContent = data.特征段名称 || '-';
        document.getElementById('lineLength').textContent = data.线路长度 ? data.线路长度 + ' km' : '-';
        document.getElementById('windSpeed').textContent = data.风速 || data.边界条件?.风速 ? (data.风速 || data.边界条件.风速) + ' m/s' : '-';
        document.getElementById('iceThickness').textContent = data.覆冰 || data.边界条件?.覆冰 ? (data.覆冰 || data.边界条件.覆冰) + ' mm' : '-';
        document.getElementById('circuitCount').textContent = data.回路数 || data.边界条件?.回路数 || '-';
        document.getElementById('wireSpec').textContent = data.导线规格 || data.边界条件?.导线规格 || data.导线型号 || data.边界条件?.导线型号 || '-';

        // 根据气象区控制tab显示
        controlTabsByWeatherZone(weatherZone);
    } catch (error) {
        console.error('加载特征段信息失败:', error);
        // 使用演示数据
        document.getElementById('sectionName').textContent = '广州段';
        document.getElementById('lineLength').textContent = '30 km';
        document.getElementById('windSpeed').textContent = '27 m/s';
        document.getElementById('iceThickness').textContent = '0 mm';
        document.getElementById('circuitCount').textContent = '双回路';
        document.getElementById('wireSpec').textContent = 'JL/LB20A-630/45';
    }
}

// 加载指标数据
async function loadIndicators() {
    if (!currentSectionId) return;
    
    try {
        // 加载本体费用指标
        const bentiResponse = await fetch(`/api/pricing/indicators/benti?section_id=${currentSectionId}`);
        const bentiData = await bentiResponse.json();
        renderBentiTable(bentiData);
        
        // 加载其他费用指标
        const qitaResponse = await fetch(`/api/pricing/indicators/qita?section_id=${currentSectionId}`);
        const qitaData = await qitaResponse.json();
        renderQitaTable(qitaData);
    } catch (error) {
        console.error('加载指标数据失败:', error);
        window.showToast('加载指标数据失败', 'error');
    }
}

// 渲染本体费用指标表格
function renderBentiTable(indicators) {
    const tbody = document.getElementById('bentiHistoryTableBody');
    if (!tbody) {
        console.error('找不到bentiHistoryTableBody元素');
        return;
    }

    tbody.innerHTML = '';
    
    // 如果没有数据，显示空状态
    if (!indicators || indicators.length === 0) {
        const tr = document.createElement('tr');
        const td = document.createElement('td');
        td.colSpan = 15; // 表头有15列
        td.style.textAlign = 'center';
        td.textContent = '未找到匹配的历史指标数据';
        tr.appendChild(td);
        tbody.appendChild(tr);
        return;
    }
    
    indicators.forEach(indicator => {
        const tr = document.createElement('tr');
        tr.className = selectedBentiIds.includes(indicator.序号) ? 'selected' : '';
        tr.setAttribute('data-id', indicator.序号); // 添加data-id属性
        
        // 创建选择列单元格
        const checkboxCell = document.createElement('td');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = selectedBentiIds.includes(indicator.序号);
        checkbox.onclick = (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            selectBentiIndicator(indicator.序号, tr);
        };
        checkboxCell.appendChild(checkbox);
        checkboxCell.onclick = (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            checkbox.checked = !checkbox.checked;
            selectBentiIndicator(indicator.序号, tr);
        };
        tr.appendChild(checkboxCell);
        
        // 添加匹配度单元格
        const matchCell = document.createElement('td');
        const matchSpan = document.createElement('span');
        matchSpan.className = 'pricing-sp-match-tag';
        matchSpan.textContent = `${indicator.matchDegree}%`;
        matchCell.appendChild(matchSpan);
        tr.appendChild(matchCell);
        
        // 添加其他单元格
        const cells = [
            indicator['工程名称'] || '-',
            indicator['线路工程'] || '-',
            indicator['线路总长度'] || '-',
            indicator['回路数'] || '-',
            indicator['风速'] || '-',
            indicator['覆冰'] || '-',
            indicator['导线规格'] || '-',
            indicator['平地'] || '-',
            indicator['丘陵'] || '-',
            indicator['山地'] || indicator['低山'] || '-',
            indicator['高山'] || indicator['中山'] || '-',
            indicator['峻岭'] || indicator['高山'] || '-',
            indicator['泥沼'] || '-',
            indicator['河网'] || '-',
            indicator['人力运距'] || '-',
            indicator['汽车运距'] || '-',
            indicator['铁塔基数'] || '-',
            indicator['直线塔'] || '-',
            indicator['耐张塔'] || '-',
            indicator['耐张比例'] || '-',
            indicator['导线'] || '-',
            indicator['塔材'] || '-',
            indicator['基础钢材'] || '-',
            indicator['地脚螺栓和插入式角钢'] || '-',
            indicator['挂线金具'] || '-',
            indicator['导线间隔棒'] || '-',
            indicator['防振锤'] || '-',
            indicator['导线防振锤'] || '-',
            indicator['地线防振锤'] || '-',
            indicator['合成复合绝缘子'] || '-',
            indicator['玻璃绝缘子盘式绝缘子'] || '-',
            indicator['硬跳'] || '-',
            indicator['现浇混凝土'] || '-',
            indicator['灌柱桩基础混凝土'] || '-',
            indicator['基础护壁'] || '-',
            indicator['基础垫层'] || '-',
            indicator['钻孔灌注桩深度'] || '-',
            indicator['护坡挡土墙'] || '-',
            indicator['土方量'] || '-',
            indicator['基坑土方非机械'] || '-',
            indicator['基坑土方机械'] || '-',
            indicator['接地槽'] || '-',
            indicator['排水沟'] || '-',
            indicator['尖峰基面'] || '-',
            indicator['本体工程'] || '-',
            indicator['基础工程'] || '-',
            indicator['杆塔工程'] || '-',
            indicator['接地工程'] || '-',
            indicator['架线工程'] || '-',
            indicator['附件工程'] || '-',
            indicator['辅助工程'] || '-'
        ];
        
        cells.forEach((value, index) => {
            const td = document.createElement('td');
            if ([4, 5, 6, 8, 9, 10, 11, 12, 13].includes(index)) {
                td.setAttribute('data-type', 'number');
            }
            td.textContent = value;
            tr.appendChild(td);
        });
        
        tbody.appendChild(tr);
    });
}

// 渲染其他费用指标表格
function renderQitaTable(indicators) {
    const tbody = document.getElementById('qitaHistoryTableBody');
    if (!tbody) {
        console.error('找不到qitaHistoryTableBody元素');
        return;
    }

    tbody.innerHTML = '';
    
    // 如果没有数据，显示空状态
    if (!indicators || indicators.length === 0) {
        const tr = document.createElement('tr');
        const td = document.createElement('td');
        td.colSpan = 8; // 表头有8列
        td.style.textAlign = 'center';
        td.textContent = '未找到匹配的历史指标数据';
        tr.appendChild(td);
        tbody.appendChild(tr);
        return;
    }
    
    indicators.forEach(indicator => {
        const tr = document.createElement('tr');
        tr.className = selectedQitaIds.includes(indicator.序号) ? 'selected' : '';
        tr.setAttribute('data-id', indicator.序号); // 添加data-id属性
        
        // 创建选择列单元格
        const checkboxCell = document.createElement('td');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = selectedQitaIds.includes(indicator.序号);
        checkbox.onclick = (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            selectQitaIndicator(indicator.序号, tr);
        };
        checkboxCell.appendChild(checkbox);
        checkboxCell.onclick = (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            checkbox.checked = !checkbox.checked;
            selectQitaIndicator(indicator.序号, tr);
        };
        tr.appendChild(checkboxCell);
        
        // 添加匹配度单元格
        const matchCell = document.createElement('td');
        const matchSpan = document.createElement('span');
        matchSpan.className = 'pricing-sp-match-tag';
        matchSpan.textContent = `${indicator.matchDegree}%`;
        matchCell.appendChild(matchSpan);
        tr.appendChild(matchCell);
        
        // 添加其他单元格
        const cells = [
            indicator['工程名称'] || '-',
            indicator['线路工程'] || '-',
            indicator['线路总长度'] || indicator['合并气象区总长度'] || '-',
            indicator['项目建设管理费'] || '-',
            indicator['项目法人管理费'] || '-',
            indicator['招标费'] || '-',
            indicator['工程监理费'] || '-',
            indicator['施工过程造价咨询及竣工结算审核费'] || '-',
            indicator['工程保险费'] || '-',
            indicator['项目建设技术服务费'] || '-',
            indicator['项目前期工作费'] || '-',
            indicator['勘察设计费'] || '-',
            indicator['勘察费'] || '-',
            indicator['设计费'] || '-',
            indicator['基本设计费'] || '-',
            indicator['其他设计费'] || '-',
            indicator['设计文件评审费'] || '-',
            indicator['可行性研究文件评审费'] || '-',
            indicator['初步设计文件评审费'] || '-',
            indicator['施工图文件评审费'] || '-',
            indicator['工程建设检测费'] || '-',
            indicator['电力工程质量检测费'] || '-',
            indicator['桩基检测费'] || '-',
            indicator['电力工程技术经济标准编制费'] || '-',
            indicator['生产准备费'] || '-',
            indicator['管理车辆购置费'] || '-',
            indicator['工器具及办公家具购置费'] || '-',
            indicator['生产职工培训及提前进场费'] || '-',
            indicator['专业爆破服务费'] || '-'
        ];
        
        cells.forEach((value, index) => {
            const td = document.createElement('td');
            if (index === 2) {
                td.setAttribute('data-type', 'number');
            }
            td.textContent = value;
            tr.appendChild(td);
        });
        
        tbody.appendChild(tr);
    });
}

// 保存指标选择
async function saveIndicators() {
    if (!currentSectionId) {
        window.showToast('请先选择特征段', 'warning');
        return;
    }
    
    try {
        const formData = {
            本体指标序号: Array.from(document.querySelectorAll('#bentiTable input[type="checkbox"]:checked')).map(cb => cb.value),
            其他指标序号: Array.from(document.querySelectorAll('#qitaTable input[type="checkbox"]:checked')).map(cb => cb.value)
        };
        
        const response = await fetch(`/api/pricing/sections/${currentSectionId}/indicators`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        if (response.ok) {
            window.showToast('指标选择保存成功');
            await loadSectionData(); // 刷新特征段列表以更新状态
        } else {
            throw new Error('保存指标选择失败');
        }
    } catch (error) {
        console.error('保存指标选择失败:', error);
        window.showToast('保存指标选择失败', 'error');
    }
} 

// 关闭模态框，返回特征段管理页面
function closeModal() {
    if (window.parent && window.parent.closeIndicatorSelectModal) {
        console.log('调用父窗口的closeIndicatorSelectModal函数');
        window.parent.closeIndicatorSelectModal();
    } else {
        console.error('找不到父窗口的closeIndicatorSelectModal函数');
    }
}

// 保存指标选择
function saveIndicatorSelection() {
    console.log('保存指标选择');
    
    // 演示模式下，直接显示成功消息
    showToast('组价计算结果保存成功', 'success');
    
    // 通知父窗口更新特征段状态
    if (window.parent && window.parent.updateSectionStatus) {
        const sectionData = {
            特征段名称: document.getElementById('sectionName').textContent,
            组价计算状态: '已组价'
        };
        window.parent.updateSectionStatus(sectionData);
    }
    
    // 关闭模态框
    setTimeout(() => {
        closeModal();
    }, 500);
}

// 搜索指标
function searchIndicators() {
    console.log('搜索历史指标');
    
    // 获取搜索条件
    const lineLength = document.getElementById('searchLineLength').value;
    const windSpeed = document.getElementById('searchWindSpeed').value;
    const iceThickness = document.getElementById('searchIceThickness').value;
    const circuitCount = document.getElementById('searchCircuitCount').value;
    const wireSpec = document.getElementById('searchWireSpec').value;
    
    console.log('搜索条件:', {lineLength, windSpeed, iceThickness, circuitCount, wireSpec});
    
    try {
        // 显示加载状态
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        } else {
            console.error('找不到loadingOverlay元素');
        }
        
        // 模拟搜索过程
        setTimeout(() => {
            try {
                // 使用demoData中的数据
                // 为每条数据添加匹配度属性
                const bentiData = demoData.benti.map(item => {
                    // 生成50-90之间的随机匹配度
                    const matchDegree = Math.floor(Math.random() * 41) + 50;
                    return { ...item, matchDegree };
                });
                
                const qitaData = demoData.qita.map(item => {
                    // 生成50-90之间的随机匹配度
                    const matchDegree = Math.floor(Math.random() * 41) + 50;
                    return { ...item, matchDegree };
                });
                
                // 按匹配度降序排序
                bentiData.sort((a, b) => b.matchDegree - a.matchDegree);
                qitaData.sort((a, b) => b.matchDegree - a.matchDegree);
                
                console.log('本体费用数据:', bentiData);
                console.log('其他费用数据:', qitaData);
                
                // 渲染表格
                renderBentiTable(bentiData);
                renderQitaTable(qitaData);
                
                // 隐藏加载状态
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
                
                // 显示成功消息
                const totalResults = bentiData.length ;
                showToast('已找到 ' + totalResults + ' 条匹配记录', 'success');
            } catch (innerError) {
                console.error('查询处理过程中出错:', innerError);
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
                showToast('查询处理失败: ' + innerError.message, 'error');
            }
        }, 500);
    } catch (error) {
        console.error('查询失败:', error);
        showToast('查询失败: ' + error.message, 'error');
    }
}

// 生成匹配度随机数据
function generateRandomData(sourceData, count) {
    const result = [];
    const usedIndexes = new Set();
    
    while (result.length < count && usedIndexes.size < sourceData.length) {
        const randomIndex = Math.floor(Math.random() * sourceData.length);
        if (!usedIndexes.has(randomIndex)) {
            usedIndexes.add(randomIndex);
            const item = { ...sourceData[randomIndex] };
            // 生成50-90之间的随机匹配度
            item.matchDegree = Math.floor(Math.random() * 41) + 50;
            result.push(item);
        }
    }
    
    // 按匹配度降序排序
    return result.sort((a, b) => b.matchDegree - a.matchDegree);
}

// 显示提示信息
function showToast(message, type = 'info') {
    // 移除可能已存在的toast
    const existingToasts = document.querySelectorAll('.pricing-sp-toast');
    existingToasts.forEach(toast => toast.remove());

    const toast = document.createElement('div');
    toast.className = `pricing-sp-toast ${type}`;
    toast.textContent = message;
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.padding = '12px 24px';
    toast.style.borderRadius = '4px';
    toast.style.zIndex = '9999';
    toast.style.animation = 'slideIn 0.3s ease-out';
    toast.style.boxShadow = '0 3px 6px rgba(0, 0, 0, 0.16)';
    toast.style.maxWidth = '300px';
    
    if (type === 'success') {
        toast.style.backgroundColor = '#f6ffed';
        toast.style.border = '1px solid #b7eb8f';
        toast.style.color = '#52c41a';
    } else if (type === 'error') {
        toast.style.backgroundColor = '#fff2f0';
        toast.style.border = '1px solid #ffccc7';
        toast.style.color = '#ff4d4f';
    } else {
        toast.style.backgroundColor = '#e6f7ff';
        toast.style.border = '1px solid #91d5ff';
        toast.style.color = '#1890ff';
    }
    
    document.body.appendChild(toast);

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);

    // 3秒后自动移除
    setTimeout(() => {
        toast.remove();
        style.remove();
    }, 3000);
}

// 切换指标面板
function switchTab(tabName, tabGroup) {
    const panels = document.querySelectorAll(`[id$="Panel-${tabGroup}"]`);
    const buttons = document.querySelectorAll(`[onclick*="switchTab"][onclick*="${tabGroup}"]`);
    
    panels.forEach(panel => {
        panel.style.display = panel.id.includes(tabName) ? 'block' : 'none';
    });
    
    buttons.forEach(button => {
        if (button.getAttribute('onclick').includes(`'${tabName}'`)) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
} 

// 选择本体费用指标
function selectBentiIndicator(indicatorId, tr) {
    console.log('选择本体费用指标:', indicatorId);
    
    // 获取复选框
    const checkbox = tr.querySelector('input[type="checkbox"]');
    const isChecked = checkbox.checked;
    
    // 更新行样式
    tr.classList.toggle('selected', isChecked);
    
    // 更新选中的指标ID数组
    if (isChecked) {
        if (!selectedBentiIds.includes(indicatorId)) {
            selectedBentiIds.push(indicatorId);
        }
    } else {
        selectedBentiIds = selectedBentiIds.filter(id => id !== indicatorId);
    }
    
    console.log('当前选中的本体费用指标:', selectedBentiIds);
}

// 选择其他费用指标
function selectQitaIndicator(indicatorId, tr) {
    console.log('选择其他费用指标:', indicatorId);
    
    // 获取复选框
    const checkbox = tr.querySelector('input[type="checkbox"]');
    const isChecked = checkbox.checked;
    
    // 更新行样式
    tr.classList.toggle('selected', isChecked);
    
    // 更新选中的指标ID数组
    if (isChecked) {
        if (!selectedQitaIds.includes(indicatorId)) {
            selectedQitaIds.push(indicatorId);
        }
    } else {
        selectedQitaIds = selectedQitaIds.filter(id => id !== indicatorId);
    }
    
    console.log('当前选中的其他费用指标:', selectedQitaIds);
} 