<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特征段组价管理</title>
    <!-- <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}"> -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pricing.css') }}">
</head>

<body class="pricing-sp-container">
    <div class="pricing-sp-section">
        <div class="pricing-sp-page-header">
            <h1 class="pricing-sp-page-title"
                style="font-size: 1.5rem; font-weight: bold; display: block !important; visibility: visible !important; opacity: 1 !important;">
                特征段组价管理</h1>

            <!-- 气象区状态显示和调试面板 -->
            <div id="weatherZoneDebugPanel" style="position: absolute; top: 10px; right: 10px; background: #f0f0f0; padding: 10px; border-radius: 5px; font-size: 12px; border: 1px solid #ccc;">
                <div style="margin-bottom: 5px;">
                    <strong>气象区状态:</strong> <span id="currentWeatherZone" style="color: #007bff; font-weight: bold;">-</span>
                </div>
                <div style="margin-bottom: 5px;">
                    <strong>调试模式:</strong>
                    <button id="toggleDebugMode" onclick="toggleDebugMode()" style="margin-left: 5px; padding: 2px 8px; font-size: 11px;">开启</button>
                </div>
                <div id="debugControls" style="display: none;">
                    <button onclick="testWeatherZone('分')" style="margin: 2px; padding: 2px 6px; font-size: 11px; background: #28a745; color: white; border: none; border-radius: 3px;">测试"分"</button>
                    <button onclick="testWeatherZone('合并')" style="margin: 2px; padding: 2px 6px; font-size: 11px; background: #dc3545; color: white; border: none; border-radius: 3px;">测试"合并"</button>
                </div>
            </div>

            <!-- 注释掉右上角按钮
        <div class="pricing-sp-page-actions">
                <button type="button" class="pricing-shared-btn pricing-shared-btn-secondary" onclick="window.parent.closeIndicatorSelectModal()">返回</button>
                <button type="button" class="pricing-shared-btn pricing-shared-btn-primary" onclick="saveIndicatorSelection()">结果保存</button>
        </div>
            -->
        </div>

        <!-- 特征段信息 -->
        <h2 class="pricing-sp-section-title">特征段信息</h2>
        <div class="pricing-sp-feature-info">
            <div class="pricing-sp-feature-info-item name">
                <label>特征段名称</label>
                <span id="sectionName">广州段</span>
            </div>
            <div class="pricing-sp-feature-info-item length">
                <label>线路长度</label>
                <span id="lineLength">30</span>
            </div>
            <div class="pricing-sp-feature-info-item wind-speed">
                <label>风速</label>
                <span id="windSpeed">27</span>
            </div>
            <div class="pricing-sp-feature-info-item ice-thickness">
                <label>覆冰</label>
                <span id="iceThickness">5</span>
            </div>
            <div class="pricing-sp-feature-info-item circuit-count">
                <label>回路数</label>
                <span id="circuitCount">双回路</span>
            </div>
            <div class="pricing-sp-feature-info-item wire-spec">
                <label>导线规格</label>
                <span id="wireSpec">JL/LB20A-630/45</span>
            </div>
        </div>

        <!-- 特征段基准指标量区域 -->
        <div style="margin-top:50px">
            <h2 class="pricing-sp-section-title">组价计算</h2>
            <div class="pricing-sp-tab-header">
                <div class="pricing-sp-indicator-tabs">
                    <button class="pricing-sp-tab-button active" onclick="switchTab('benti', 'base')">本体费用指标</button>
                    <button class="pricing-sp-tab-button" onclick="switchTab('qita', 'base')">其他费用指标</button>
                    <button class="pricing-shared-btn pricing-shared-btn-primary"
                        onclick="showHistoryIndicatorModal()">基准指标创建</button>
                    <button class="pricing-shared-btn pricing-shared-btn-primary pricing-sp-btn-edit"
                        onclick="editBaseIndicator()">基准指标编辑</button>
                    <button class="pricing-shared-btn pricing-shared-btn-primary pricing-sp-btn-manage"
                    onclick="managePrice()">单价管理</button>
                    <button class="pricing-shared-btn pricing-shared-btn-primary"
                        onclick="calculatePrice()">指标组价计算</button>
                </div>
            </div>

            <!-- 本体费用基准指标面板 -->
            <div id="bentiPanel-base" class="pricing-sp-indicator-panel">
                <div class="pricing-sp-table-container">
                    <table class="pricing-sp-indicator-table">
                        <thead>
                            <tr>
                                <th>数量|价格</th>
                                <th>铁塔基数(基)</th>
                                <th>直线塔(基)</th>
                                <th>耐张塔(基)</th>
                                <th>耐张比例(%)</th>
                                <th>导线(t)</th>
                                <th>塔材(t)</th>
                                <th>基础钢材(t)</th>
                                <th>地脚螺栓和插入式角钢(t)</th>
                                <th>挂线金具(t)</th>
                                <th>导线间隔棒(套)</th>
                                <th>防振锤(个)</th>
                                <th>导线防振锤(个)</th>
                                <th>地线防振锤(个)</th>
                                <th>合成复合绝缘子(支)</th>
                                <th>玻璃绝缘子/盘式绝缘子(支)</th>
                                <th>硬跳(套)</th>
                                <th>现浇混凝土(m³)</th>
                                <th>灌柱桩基础混凝土(m³)</th>
                                <th>基础护壁(m³)</th>
                                <th>基础垫层(m³)</th>
                                <th>钻孔灌注桩深度(m)</th>
                                <th>护坡、挡土墙(m³)</th>
                                <th>土方量(m³)</th>
                                <th>基坑土方（非机械）(m³)</th>
                                <th>基坑土方（机械）(m³)</th>
                                <th>接地槽(m³)</th>
                                <th>排水沟(m³)</th>
                                <th>尖峰、基面(m³)</th>
                                <!-- 本体指标，不需要展示费用列 -->
                                <!-- <th>本体工程(万元)</th>
                                <th>基础工程(万元)</th>
                                <th>杆塔工程(万元)</th>
                                <th>接地工程(万元)</th>
                                <th>架线工程(万元)</th>
                                <th>附件工程(万元)</th>
                                <th>辅助工程(万元)</th> -->
                            </tr>
                        </thead>
                        <tbody id="bentiBaseTableBody">
                            <!-- 默认显示五行数据 -->
                        </tbody>
                    </table>
                </div>

                <!-- 本体工程数据表格 -->
                <div class="pricing-sp-table-container" style="margin-top: 20px;">
                    <table class="pricing-sp-indicator-table">
                        <thead>
                            <tr>
                                <th>本体工程(万元)</th>
                                <th>指标单公里基准 f</th>
                                <th>指标单公里组价 g=c(基础+塔材+导线）*0.7/10000</th>
                                <th>总价 h=g*长度</th>
                            </tr>
                        </thead>
                        <tbody id="bentiProjectTableBody">
                            <!-- 本体工程数据 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 其他费用基准指标面板 -->
            <div id="qitaPanel-base" class="pricing-sp-indicator-panel" style="display: none;">
                <div class="pricing-sp-table-container">
                    <table class="pricing-sp-indicator-table">
                        <thead>
                            <tr>
                                <th>数量|价格</th>
                                <th>项目建设管理费（万元）</th>
                                <th>项目法人管理费（万元）</th>
                                <th>招标费（万元）</th>
                                <th>工程监理费（万元）</th>
                                <th>施工过程造价咨询及竣工结算审核费（万元）</th>
                                <th>工程保险费（万元）</th>
                                <th>项目建设技术服务费（万元）</th>
                                <th>项目前期工作费（万元）</th>
                                <th>勘察设计费（万元）</th>
                                <th>勘察费（万元）</th>
                                <th>设计费（万元）</th>
                                <th>基本设计费（万元）</th>
                                <th>其他设计费（万元）</th>
                                <th>设计文件评审费（万元）</th>
                                <th>可行性研究文件评审费（万元）</th>
                                <th>初步设计文件评审费（万元）</th>
                                <th>施工图文件评审费（万元）</th>
                                <th>工程建设检测费（万元）</th>
                                <th>电力工程质量检测费（万元）</th>
                                <th>桩基检测费（万元）</th>
                                <th>电力工程技术经济标准编制费（万元）</th>
                                <th>生产准备费（万元）</th>
                                <th>管理车辆购置费（万元）</th>
                                <th>工器具及办公家具购置费（万元）</th>
                                <th>生产职工培训及提前进场费（万元）</th>
                                <th>专业爆破服务费（万元）</th>
                            </tr>
                        </thead>
                        <tbody id="qitaBaseTableBody">
                            <!-- 默认显示四行数据 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载状态遮罩 -->
    <div class="pricing-sp-loading-overlay" id="loadingOverlay">
        <div class="pricing-shared-loading-spinner"></div>
    </div>

    <!-- 新的底部按钮 -->
    <div class="pricing-sp-bottom-buttons">
        <button type="button" class="pricing-shared-btn pricing-shared-btn-secondary" onclick="closeModal()">返回</button>
        <button type="button" class="pricing-shared-btn pricing-shared-btn-primary"
            onclick="saveIndicatorSelection()">结果保存</button>
    </div>

    <!-- 历史指标查询弹出层 -->
    <div id="historyIndicatorModal" class="pricing-shared-modal pricing-sp-history-modal">
        <div class="pricing-shared-modal-content pricing-sp-history-content">
            <div class="pricing-sp-history-header">
                <div class="pricing-sp-history-title">历史指标查询</div>
                <div class="pricing-sp-history-close" onclick="hideHistoryIndicatorModal()">×</div>
            </div>
            <div class="pricing-sp-history-body">
                <!-- 查询条件区域 -->
                <div class="pricing-sp-search-section">
                    <h2 class="pricing-sp-section-title">查询条件</h2>
                    <div class="pricing-sp-search-grid">
                        <div class="pricing-sp-search-item">
                            <label class="pricing-sp-search-label">线路长度(km)</label>
                            <input type="number" id="searchLineLength"
                                class="pricing-shared-input pricing-sp-search-input" placeholder="请输入线路长度">
                        </div>
                        <div class="pricing-sp-search-item">
                            <label class="pricing-sp-search-label">风速(m/s)</label>
                            <input type="number" id="searchWindSpeed"
                                class="pricing-shared-input pricing-sp-search-input" placeholder="请输入风速">
                        </div>
                        <div class="pricing-sp-search-item">
                            <label class="pricing-sp-search-label">覆冰(mm)</label>
                            <input type="number" id="searchIceThickness"
                                class="pricing-shared-input pricing-sp-search-input" placeholder="请输入覆冰厚度">
                        </div>
                        <div class="pricing-sp-search-item">
                            <label class="pricing-sp-search-label">回路数</label>
                            <select id="searchCircuitCount" class="pricing-shared-select pricing-sp-search-input">
                                <option value="">全部</option>
                                <option value="单回路">单回路</option>
                                <option value="双回路">双回路</option>
                            </select>
                        </div>
                        <div class="pricing-sp-search-item">
                            <label class="pricing-sp-search-label">导线规格</label>
                            <input type="text" id="searchWireSpec" class="pricing-shared-input pricing-sp-search-input"
                                placeholder="请输入导线规格">
                        </div>
                        <div class="pricing-sp-search-actions">
                            <button type="button" class="pricing-shared-btn pricing-shared-btn-primary"
                                onclick="searchIndicators()">查询</button>
                        </div>
                    </div>
                </div>

                <!-- 查询结果标签页 -->
                <div class="pricing-sp-tab-header">
                    <div class="pricing-sp-indicator-tabs">
                        <button class="pricing-sp-tab-button active"
                            onclick="switchTab('benti', 'history')">本体费用指标</button>
                        <button class="pricing-sp-tab-button" onclick="switchTab('qita', 'history')">其他费用指标</button>
                    </div>
                </div>

                <!-- 本体费用指标面板 -->
                <div id="bentiPanel-history" class="pricing-sp-indicator-panel">
                    <div class="pricing-sp-table-container">
                        <table class="pricing-sp-indicator-table">
                            <thead>
                                <tr>
                                    <th>选择</th>
                                    <th>匹配度</th>
                                    <th>工程名称</th>
                                    <th>线路工程</th>
                                    <th>线路总长度(km)</th>
                                    <th>回路数</th>
                                    <th>风速(m/s)</th>
                                    <th>覆冰(mm)</th>
                                    <th>导线规格</th>
                                    <th>平地(%)</th>
                                    <th>丘陵(%)</th>
                                    <th>山地(%)</th>
                                    <th>高山(%)</th>
                                    <th>峻岭(%)</th>
                                    <th>泥沼(%)</th>
                                    <th>河网(%)</th>
                                    <th>人力运距(km)</th>
                                    <th>汽车运距（含拖拉机）(km)</th>
                                    <th>铁塔基数(基)</th>
                                    <th>直线塔(基)</th>
                                    <th>耐张塔(基)</th>
                                    <th>耐张比例(%)</th>
                                    <th>导线(t)</th>
                                    <th>塔材(t)</th>
                                    <th>基础钢材(t)</th>
                                    <th>地脚螺栓和插入式角钢(t)</th>
                                    <th>挂线金具(t)</th>
                                    <th>导线间隔棒(套)</th>
                                    <th>防振锤(个)</th>
                                    <th>导线防振锤(个)</th>
                                    <th>地线防振锤(个)</th>
                                    <th>合成复合绝缘子(支)</th>
                                    <th>玻璃绝缘子/盘式绝缘子(支)</th>
                                    <th>硬跳(套)</th>
                                    <th>现浇混凝土(m³)</th>
                                    <th>灌柱桩基础混凝土(m³)</th>
                                    <th>基础护壁(m³)</th>
                                    <th>基础垫层(m³)</th>
                                    <th>钻孔灌注桩深度(m)</th>
                                    <th>护坡、挡土墙(m³)</th>
                                    <th>土方量(m³)</th>
                                    <th>基坑土方（非机械）(m³)</th>
                                    <th>基坑土方（机械）(m³)</th>
                                    <th>接地槽(m³)</th>
                                    <th>排水沟(m³)</th>
                                    <th>尖峰、基面(m³)</th>
                                    <th>本体工程(万元)</th>
                                    <th>基础工程(万元)</th>
                                    <th>杆塔工程(万元)</th>
                                    <th>接地工程(万元)</th>
                                    <th>架线工程(万元)</th>
                                    <th>附件工程(万元)</th>
                                    <th>辅助工程(万元)</th>
                                </tr>
                            </thead>
                            <tbody id="bentiHistoryTableBody">
                                <!-- 历史指标数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 其他费用指标面板 -->
                <div id="qitaPanel-history" class="pricing-sp-indicator-panel" style="display:none;">
                    <div class="pricing-sp-table-container">
                        <table class="pricing-sp-indicator-table">
                            <thead>
                                <tr>
                                    <th>选择</th>
                                    <th>匹配度</th>
                                    <th>工程名称</th>
                                    <th>线路工程</th>
                                    <th>合并气象区总长度</th>
                                    <th>项目建设管理费（万元）</th>
                                    <th>项目法人管理费（万元）</th>
                                    <th>招标费（万元）</th>
                                    <th>工程监理费（万元）</th>
                                    <th>施工过程造价咨询及竣工结算审核费（万元）</th>
                                    <th>工程保险费（万元）</th>
                                    <th>项目建设技术服务费（万元）</th>
                                    <th>项目前期工作费（万元）</th>
                                    <th>勘察设计费（万元）</th>
                                    <th>勘察费（万元）</th>
                                    <th>设计费（万元）</th>
                                    <th>基本设计费（万元）</th>
                                    <th>其他设计费（万元）</th>
                                    <th>设计文件评审费（万元）</th>
                                    <th>可行性研究文件评审费（万元）</th>
                                    <th>初步设计文件评审费（万元）</th>
                                    <th>施工图文件评审费（万元）</th>
                                    <th>工程建设检测费（万元）</th>
                                    <th>电力工程质量检测费（万元）</th>
                                    <th>桩基检测费（万元）</th>
                                    <th>电力工程技术经济标准编制费（万元）</th>
                                    <th>生产准备费（万元）</th>
                                    <th>管理车辆购置费（万元）</th>
                                    <th>工器具及办公家具购置费（万元）</th>
                                    <th>生产职工培训及提前进场费（万元）</th>
                                    <th>专业爆破服务费（万元）</th>
                                </tr>
                            </thead>
                            <tbody id="qitaHistoryTableBody">
                                <!-- 历史指标数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="pricing-sp-modal-actions">
                <button type="button" class="pricing-shared-btn pricing-shared-btn-primary"
                    onclick="confirmCreateBaseIndicator()">保存</button>
                <button type="button" class="pricing-shared-btn pricing-shared-btn-secondary"
                    onclick="hideHistoryIndicatorModal()">取消</button>
            </div>
        </div>
    </div>
    </div>

    <!-- 创建基准指标确认框 -->
    <div id="confirmModal" class="pricing-shared-modal pricing-sp-confirm-modal" style="display: none;">
        <div class="pricing-shared-modal-content pricing-sp-confirm-content">
            <div class="pricing-sp-modal-title">创建基准指标</div>
            <div>是否将所选历史指标数据创建为基准指标，如所选历史指标为多条，将进行加权平均做为基准指标。</div>
            <div class="pricing-sp-modal-buttons">
                <button class="pricing-shared-btn pricing-shared-btn-secondary" onclick="hideModal()">取消</button>
                <button class="pricing-shared-btn pricing-shared-btn-primary" onclick="confirmModal()">确定</button>
            </div>
        </div>
    </div>

    <!-- 单价管理模态框 -->
    <div id="priceManageModal" class="pricing-shared-modal pricing-sp-price-modal" style="display: none;">
        <div class="pricing-shared-modal-content pricing-sp-price-content">
            <div class="pricing-sp-modal-header">
                <div class="pricing-sp-modal-title">单价管理</div>
                <div class="pricing-sp-modal-close" onclick="hidePriceModal()">×</div>
            </div>
            <div class="pricing-sp-price-body">
                <div class="pricing-sp-tab-header">
                    <div class="pricing-sp-indicator-tabs">
                        <button class="pricing-sp-tab-button active" onclick="switchTab('benti', 'price')">本体费用指标</button>
                        <button class="pricing-sp-tab-button" onclick="switchTab('qita', 'price')">其他费用指标</button>
                    </div>
                </div>
                
                <!-- 本体费用单价面板 -->
                <div id="bentiPanel-price" class="pricing-sp-indicator-panel">
                    <div class="pricing-sp-table-container">
                        <table class="pricing-sp-indicator-table">
                            <thead>
                                <tr>
                                    <th>指标名称</th>
                                    <th>计量单位</th>
                                    <th>单价(元)</th>
                                </tr>
                            </thead>
                            <tbody id="bentiPriceTableBody">
                                <!-- 本体费用单价数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 其他费用单价面板 -->
                <div id="qitaPanel-price" class="pricing-sp-indicator-panel" style="display:none;">
                    <div class="pricing-sp-table-container">
                        <table class="pricing-sp-indicator-table">
                            <thead>
                                <tr>
                                    <th>指标名称</th>
                                    <th>计量单位</th>
                                    <th>单价(元)</th>
                                </tr>
                            </thead>
                            <tbody id="qitaPriceTableBody">
                                <!-- 其他费用单价数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="pricing-sp-modal-actions">
                <button type="button" class="pricing-shared-btn pricing-shared-btn-primary" onclick="savePriceData()">保存</button>
                <button type="button" class="pricing-shared-btn pricing-shared-btn-secondary" onclick="hidePriceModal()">取消</button>
        </div>
    </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>
    <script src="{{ url_for('static', filename='js/section_pricingSum.js') }}"></script>
</body>

</html>